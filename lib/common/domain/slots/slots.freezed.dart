// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'slots.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ANSlotsModel {
  String get id;
  DateTime get starts;
  DateTime? get ends;

  /// Create a copy of ANSlotsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ANSlotsModelCopyWith<ANSlotsModel> get copyWith =>
      _$ANSlotsModelCopyWithImpl<ANSlotsModel>(
          this as ANSlotsModel, _$identity);

  /// Serializes this ANSlotsModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ANSlotsModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.starts, starts) || other.starts == starts) &&
            (identical(other.ends, ends) || other.ends == ends));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, starts, ends);

  @override
  String toString() {
    return 'ANSlotsModel(id: $id, starts: $starts, ends: $ends)';
  }
}

/// @nodoc
abstract mixin class $ANSlotsModelCopyWith<$Res> {
  factory $ANSlotsModelCopyWith(
          ANSlotsModel value, $Res Function(ANSlotsModel) _then) =
      _$ANSlotsModelCopyWithImpl;
  @useResult
  $Res call({String id, DateTime starts, DateTime? ends});
}

/// @nodoc
class _$ANSlotsModelCopyWithImpl<$Res> implements $ANSlotsModelCopyWith<$Res> {
  _$ANSlotsModelCopyWithImpl(this._self, this._then);

  final ANSlotsModel _self;
  final $Res Function(ANSlotsModel) _then;

  /// Create a copy of ANSlotsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? starts = null,
    Object? ends = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      starts: null == starts
          ? _self.starts
          : starts // ignore: cast_nullable_to_non_nullable
              as DateTime,
      ends: freezed == ends
          ? _self.ends
          : ends // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// Adds pattern-matching-related methods to [ANSlotsModel].
extension ANSlotsModelPatterns on ANSlotsModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ANSlotsModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANSlotsModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ANSlotsModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANSlotsModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ANSlotsModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANSlotsModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String id, DateTime starts, DateTime? ends)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANSlotsModel() when $default != null:
        return $default(_that.id, _that.starts, _that.ends);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String id, DateTime starts, DateTime? ends) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANSlotsModel():
        return $default(_that.id, _that.starts, _that.ends);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String id, DateTime starts, DateTime? ends)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANSlotsModel() when $default != null:
        return $default(_that.id, _that.starts, _that.ends);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ANSlotsModel implements ANSlotsModel {
  const _ANSlotsModel({required this.id, required this.starts, this.ends});
  factory _ANSlotsModel.fromJson(Map<String, dynamic> json) =>
      _$ANSlotsModelFromJson(json);

  @override
  final String id;
  @override
  final DateTime starts;
  @override
  final DateTime? ends;

  /// Create a copy of ANSlotsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ANSlotsModelCopyWith<_ANSlotsModel> get copyWith =>
      __$ANSlotsModelCopyWithImpl<_ANSlotsModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ANSlotsModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ANSlotsModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.starts, starts) || other.starts == starts) &&
            (identical(other.ends, ends) || other.ends == ends));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, starts, ends);

  @override
  String toString() {
    return 'ANSlotsModel(id: $id, starts: $starts, ends: $ends)';
  }
}

/// @nodoc
abstract mixin class _$ANSlotsModelCopyWith<$Res>
    implements $ANSlotsModelCopyWith<$Res> {
  factory _$ANSlotsModelCopyWith(
          _ANSlotsModel value, $Res Function(_ANSlotsModel) _then) =
      __$ANSlotsModelCopyWithImpl;
  @override
  @useResult
  $Res call({String id, DateTime starts, DateTime? ends});
}

/// @nodoc
class __$ANSlotsModelCopyWithImpl<$Res>
    implements _$ANSlotsModelCopyWith<$Res> {
  __$ANSlotsModelCopyWithImpl(this._self, this._then);

  final _ANSlotsModel _self;
  final $Res Function(_ANSlotsModel) _then;

  /// Create a copy of ANSlotsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? starts = null,
    Object? ends = freezed,
  }) {
    return _then(_ANSlotsModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      starts: null == starts
          ? _self.starts
          : starts // ignore: cast_nullable_to_non_nullable
              as DateTime,
      ends: freezed == ends
          ? _self.ends
          : ends // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

// dart format on
