import 'package:freezed_annotation/freezed_annotation.dart';

part 'slots.freezed.dart';
part 'slots.g.dart';

@freezed
class ANSlotsModel with _$ANSlotsModel {
  const factory ANSlotsModel({
    required String id,
    required DateTime starts,
    DateTime? ends,
  }) = _ANSlotsModel;

  factory ANSlotsModel.fromJson(Map<String, Object?> json) =>
      _$ANSlotsModelFromJson(json);
}

// Extension to add custom serialization methods
extension ANSlotsModelExtensions on ANSlotsModel {
  // Custom method to convert to server format without timezone conversion
  Map<String, dynamic> toMapForServer() {
    return {
      'id': id,
      // Format as local time string to prevent UTC conversion
      'starts': '${starts.year.toString().padLeft(4, '0')}-${starts.month.toString().padLeft(2, '0')}-${starts.day.toString().padLeft(2, '0')}T${starts.hour.toString().padLeft(2, '0')}:${starts.minute.toString().padLeft(2, '0')}:${starts.second.toString().padLeft(2, '0')}',
      'ends': ends != null ? '${ends!.year.toString().padLeft(4, '0')}-${ends!.month.toString().padLeft(2, '0')}-${ends!.day.toString().padLeft(2, '0')}T${ends!.hour.toString().padLeft(2, '0')}:${ends!.minute.toString().padLeft(2, '0')}:${ends!.second.toString().padLeft(2, '0')}' : null,
    };
  }
}
