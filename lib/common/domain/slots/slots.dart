import 'package:freezed_annotation/freezed_annotation.dart';

part 'slots.freezed.dart';

@freezed
class ANSlotsModel with _$ANSlotsModel {
  const factory ANSlotsModel({
    required String id,
    required DateTime starts,
    DateTime? ends,
  }) = _ANSlotsModel;

  factory ANSlotsModel.fromJson(Map<String, Object?> json) {
    try {
      // Parse time and handle UTC conversion properly
      DateTime startsTime;
      DateTime? endsTime;
      
      if (json['starts'] != null) {
        final startsString = json['starts'] as String;
        // If the time string has 'Z' suffix, it's UTC and needs to be converted to local
        if (startsString.endsWith('Z')) {
          startsTime = DateTime.parse(startsString).toLocal();
          print("Converted UTC time ${startsString} to local time: ${startsTime.toString()}");
        } else {
          startsTime = DateTime.parse(startsString);
        }
      } else {
        throw FormatException('starts field is required');
      }
      
      // Handle ends field
      if (json['ends'] != null) {
        final endsString = json['ends'] as String;
        if (endsString.endsWith('Z')) {
          endsTime = DateTime.parse(endsString).toLocal();
        } else {
          endsTime = DateTime.parse(endsString);
        }
      } else {
        // If ends is missing, default to 30 minutes after starts
        endsTime = startsTime.add(const Duration(minutes: 30));
      }

      return _ANSlotsModel(
        id: json['id'] as String,
        starts: startsTime,
        ends: endsTime,
      );    } catch (e) {
      print("Error parsing ANSlotsModel: $e");
      print("JSON: $json");
      rethrow;
    }
  }
}

// Extension to add custom serialization methods
extension ANSlotsModelExtensions on ANSlotsModel {
  // Custom method to convert to server format without timezone conversion
  Map<String, dynamic> toMapForServer() {
    return {
      'id': id,
      // Format as local time string to prevent UTC conversion
      'starts': '${starts.year.toString().padLeft(4, '0')}-${starts.month.toString().padLeft(2, '0')}-${starts.day.toString().padLeft(2, '0')}T${starts.hour.toString().padLeft(2, '0')}:${starts.minute.toString().padLeft(2, '0')}:${starts.second.toString().padLeft(2, '0')}',
      'ends': ends != null ? '${ends!.year.toString().padLeft(4, '0')}-${ends!.month.toString().padLeft(2, '0')}-${ends!.day.toString().padLeft(2, '0')}T${ends!.hour.toString().padLeft(2, '0')}:${ends!.minute.toString().padLeft(2, '0')}:${ends!.second.toString().padLeft(2, '0')}' : null,
    };
  }
}
