// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ANNotificationModel {
  int? get id => throw _privateConstructorUsedError;
  int? get nid => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  String get body =>
      throw _privateConstructorUsedError; // required String dataType,
// String? data,
  bool? get isSeen => throw _privateConstructorUsedError;
  DateTime? get date => throw _privateConstructorUsedError;

  /// Create a copy of ANNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ANNotificationModelCopyWith<ANNotificationModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANNotificationModelCopyWith<$Res> {
  factory $ANNotificationModelCopyWith(
          ANNotificationModel value, $Res Function(ANNotificationModel) then) =
      _$ANNotificationModelCopyWithImpl<$Res, ANNotificationModel>;
  @useResult
  $Res call(
      {int? id,
      int? nid,
      String title,
      String message,
      String body,
      bool? isSeen,
      DateTime? date});
}

/// @nodoc
class _$ANNotificationModelCopyWithImpl<$Res, $Val extends ANNotificationModel>
    implements $ANNotificationModelCopyWith<$Res> {
  _$ANNotificationModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ANNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? nid = freezed,
    Object? title = null,
    Object? message = null,
    Object? body = null,
    Object? isSeen = freezed,
    Object? date = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      nid: freezed == nid
          ? _value.nid
          : nid // ignore: cast_nullable_to_non_nullable
              as int?,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      body: null == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as String,
      isSeen: freezed == isSeen
          ? _value.isSeen
          : isSeen // ignore: cast_nullable_to_non_nullable
              as bool?,
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ANNotificationModelImplCopyWith<$Res>
    implements $ANNotificationModelCopyWith<$Res> {
  factory _$$ANNotificationModelImplCopyWith(_$ANNotificationModelImpl value,
          $Res Function(_$ANNotificationModelImpl) then) =
      __$$ANNotificationModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      int? nid,
      String title,
      String message,
      String body,
      bool? isSeen,
      DateTime? date});
}

/// @nodoc
class __$$ANNotificationModelImplCopyWithImpl<$Res>
    extends _$ANNotificationModelCopyWithImpl<$Res, _$ANNotificationModelImpl>
    implements _$$ANNotificationModelImplCopyWith<$Res> {
  __$$ANNotificationModelImplCopyWithImpl(_$ANNotificationModelImpl _value,
      $Res Function(_$ANNotificationModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ANNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? nid = freezed,
    Object? title = null,
    Object? message = null,
    Object? body = null,
    Object? isSeen = freezed,
    Object? date = freezed,
  }) {
    return _then(_$ANNotificationModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      nid: freezed == nid
          ? _value.nid
          : nid // ignore: cast_nullable_to_non_nullable
              as int?,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      body: null == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as String,
      isSeen: freezed == isSeen
          ? _value.isSeen
          : isSeen // ignore: cast_nullable_to_non_nullable
              as bool?,
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _$ANNotificationModelImpl extends _ANNotificationModel {
  const _$ANNotificationModelImpl(
      {this.id,
      this.nid,
      required this.title,
      required this.message,
      required this.body,
      this.isSeen = false,
      this.date})
      : super._();

  @override
  final int? id;
  @override
  final int? nid;
  @override
  final String title;
  @override
  final String message;
  @override
  final String body;
// required String dataType,
// String? data,
  @override
  @JsonKey()
  final bool? isSeen;
  @override
  final DateTime? date;

  @override
  String toString() {
    return 'ANNotificationModel(id: $id, nid: $nid, title: $title, message: $message, body: $body, isSeen: $isSeen, date: $date)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ANNotificationModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.nid, nid) || other.nid == nid) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.body, body) || other.body == body) &&
            (identical(other.isSeen, isSeen) || other.isSeen == isSeen) &&
            (identical(other.date, date) || other.date == date));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, id, nid, title, message, body, isSeen, date);

  /// Create a copy of ANNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ANNotificationModelImplCopyWith<_$ANNotificationModelImpl> get copyWith =>
      __$$ANNotificationModelImplCopyWithImpl<_$ANNotificationModelImpl>(
          this, _$identity);
}

abstract class _ANNotificationModel extends ANNotificationModel {
  const factory _ANNotificationModel(
      {final int? id,
      final int? nid,
      required final String title,
      required final String message,
      required final String body,
      final bool? isSeen,
      final DateTime? date}) = _$ANNotificationModelImpl;
  const _ANNotificationModel._() : super._();

  @override
  int? get id;
  @override
  int? get nid;
  @override
  String get title;
  @override
  String get message;
  @override
  String get body; // required String dataType,
// String? data,
  @override
  bool? get isSeen;
  @override
  DateTime? get date;

  /// Create a copy of ANNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ANNotificationModelImplCopyWith<_$ANNotificationModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
