// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ANNotificationModel {
  int? get id;
  int? get nid;
  String get title;
  String get message;
  String get body; // required String dataType,
// String? data,
  bool? get isSeen;
  DateTime? get date;

  /// Create a copy of ANNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ANNotificationModelCopyWith<ANNotificationModel> get copyWith =>
      _$ANNotificationModelCopyWithImpl<ANNotificationModel>(
          this as ANNotificationModel, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ANNotificationModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.nid, nid) || other.nid == nid) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.body, body) || other.body == body) &&
            (identical(other.isSeen, isSeen) || other.isSeen == isSeen) &&
            (identical(other.date, date) || other.date == date));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, id, nid, title, message, body, isSeen, date);

  @override
  String toString() {
    return 'ANNotificationModel(id: $id, nid: $nid, title: $title, message: $message, body: $body, isSeen: $isSeen, date: $date)';
  }
}

/// @nodoc
abstract mixin class $ANNotificationModelCopyWith<$Res> {
  factory $ANNotificationModelCopyWith(
          ANNotificationModel value, $Res Function(ANNotificationModel) _then) =
      _$ANNotificationModelCopyWithImpl;
  @useResult
  $Res call(
      {int? id,
      int? nid,
      String title,
      String message,
      String body,
      bool? isSeen,
      DateTime? date});
}

/// @nodoc
class _$ANNotificationModelCopyWithImpl<$Res>
    implements $ANNotificationModelCopyWith<$Res> {
  _$ANNotificationModelCopyWithImpl(this._self, this._then);

  final ANNotificationModel _self;
  final $Res Function(ANNotificationModel) _then;

  /// Create a copy of ANNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? nid = freezed,
    Object? title = null,
    Object? message = null,
    Object? body = null,
    Object? isSeen = freezed,
    Object? date = freezed,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      nid: freezed == nid
          ? _self.nid
          : nid // ignore: cast_nullable_to_non_nullable
              as int?,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      body: null == body
          ? _self.body
          : body // ignore: cast_nullable_to_non_nullable
              as String,
      isSeen: freezed == isSeen
          ? _self.isSeen
          : isSeen // ignore: cast_nullable_to_non_nullable
              as bool?,
      date: freezed == date
          ? _self.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// Adds pattern-matching-related methods to [ANNotificationModel].
extension ANNotificationModelPatterns on ANNotificationModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ANNotificationModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANNotificationModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ANNotificationModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANNotificationModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ANNotificationModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANNotificationModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(int? id, int? nid, String title, String message,
            String body, bool? isSeen, DateTime? date)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANNotificationModel() when $default != null:
        return $default(_that.id, _that.nid, _that.title, _that.message,
            _that.body, _that.isSeen, _that.date);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(int? id, int? nid, String title, String message,
            String body, bool? isSeen, DateTime? date)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANNotificationModel():
        return $default(_that.id, _that.nid, _that.title, _that.message,
            _that.body, _that.isSeen, _that.date);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(int? id, int? nid, String title, String message,
            String body, bool? isSeen, DateTime? date)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANNotificationModel() when $default != null:
        return $default(_that.id, _that.nid, _that.title, _that.message,
            _that.body, _that.isSeen, _that.date);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _ANNotificationModel extends ANNotificationModel {
  const _ANNotificationModel(
      {this.id,
      this.nid,
      required this.title,
      required this.message,
      required this.body,
      this.isSeen = false,
      this.date})
      : super._();

  @override
  final int? id;
  @override
  final int? nid;
  @override
  final String title;
  @override
  final String message;
  @override
  final String body;
// required String dataType,
// String? data,
  @override
  @JsonKey()
  final bool? isSeen;
  @override
  final DateTime? date;

  /// Create a copy of ANNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ANNotificationModelCopyWith<_ANNotificationModel> get copyWith =>
      __$ANNotificationModelCopyWithImpl<_ANNotificationModel>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ANNotificationModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.nid, nid) || other.nid == nid) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.body, body) || other.body == body) &&
            (identical(other.isSeen, isSeen) || other.isSeen == isSeen) &&
            (identical(other.date, date) || other.date == date));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, id, nid, title, message, body, isSeen, date);

  @override
  String toString() {
    return 'ANNotificationModel(id: $id, nid: $nid, title: $title, message: $message, body: $body, isSeen: $isSeen, date: $date)';
  }
}

/// @nodoc
abstract mixin class _$ANNotificationModelCopyWith<$Res>
    implements $ANNotificationModelCopyWith<$Res> {
  factory _$ANNotificationModelCopyWith(_ANNotificationModel value,
          $Res Function(_ANNotificationModel) _then) =
      __$ANNotificationModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int? id,
      int? nid,
      String title,
      String message,
      String body,
      bool? isSeen,
      DateTime? date});
}

/// @nodoc
class __$ANNotificationModelCopyWithImpl<$Res>
    implements _$ANNotificationModelCopyWith<$Res> {
  __$ANNotificationModelCopyWithImpl(this._self, this._then);

  final _ANNotificationModel _self;
  final $Res Function(_ANNotificationModel) _then;

  /// Create a copy of ANNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? nid = freezed,
    Object? title = null,
    Object? message = null,
    Object? body = null,
    Object? isSeen = freezed,
    Object? date = freezed,
  }) {
    return _then(_ANNotificationModel(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      nid: freezed == nid
          ? _self.nid
          : nid // ignore: cast_nullable_to_non_nullable
              as int?,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      body: null == body
          ? _self.body
          : body // ignore: cast_nullable_to_non_nullable
              as String,
      isSeen: freezed == isSeen
          ? _self.isSeen
          : isSeen // ignore: cast_nullable_to_non_nullable
              as bool?,
      date: freezed == date
          ? _self.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

// dart format on
