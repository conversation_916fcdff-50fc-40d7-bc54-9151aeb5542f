// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'image.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ANImageModel {
  String get id;
  @JsonKey(name: 'filename')
  String get fileName;
  String get url;

  /// Create a copy of ANImageModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ANImageModelCopyWith<ANImageModel> get copyWith =>
      _$ANImageModelCopyWithImpl<ANImageModel>(
          this as ANImageModel, _$identity);

  /// Serializes this ANImageModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ANImageModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.fileName, fileName) ||
                other.fileName == fileName) &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, fileName, url);

  @override
  String toString() {
    return 'ANImageModel(id: $id, fileName: $fileName, url: $url)';
  }
}

/// @nodoc
abstract mixin class $ANImageModelCopyWith<$Res> {
  factory $ANImageModelCopyWith(
          ANImageModel value, $Res Function(ANImageModel) _then) =
      _$ANImageModelCopyWithImpl;
  @useResult
  $Res call(
      {String id, @JsonKey(name: 'filename') String fileName, String url});
}

/// @nodoc
class _$ANImageModelCopyWithImpl<$Res> implements $ANImageModelCopyWith<$Res> {
  _$ANImageModelCopyWithImpl(this._self, this._then);

  final ANImageModel _self;
  final $Res Function(ANImageModel) _then;

  /// Create a copy of ANImageModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? fileName = null,
    Object? url = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      fileName: null == fileName
          ? _self.fileName
          : fileName // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _self.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [ANImageModel].
extension ANImageModelPatterns on ANImageModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ANImageModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANImageModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ANImageModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANImageModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ANImageModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANImageModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String id, @JsonKey(name: 'filename') String fileName, String url)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANImageModel() when $default != null:
        return $default(_that.id, _that.fileName, _that.url);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String id, @JsonKey(name: 'filename') String fileName, String url)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANImageModel():
        return $default(_that.id, _that.fileName, _that.url);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String id, @JsonKey(name: 'filename') String fileName, String url)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANImageModel() when $default != null:
        return $default(_that.id, _that.fileName, _that.url);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ANImageModel implements ANImageModel {
  const _ANImageModel(
      {required this.id,
      @JsonKey(name: 'filename') required this.fileName,
      required this.url});
  factory _ANImageModel.fromJson(Map<String, dynamic> json) =>
      _$ANImageModelFromJson(json);

  @override
  final String id;
  @override
  @JsonKey(name: 'filename')
  final String fileName;
  @override
  final String url;

  /// Create a copy of ANImageModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ANImageModelCopyWith<_ANImageModel> get copyWith =>
      __$ANImageModelCopyWithImpl<_ANImageModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ANImageModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ANImageModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.fileName, fileName) ||
                other.fileName == fileName) &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, fileName, url);

  @override
  String toString() {
    return 'ANImageModel(id: $id, fileName: $fileName, url: $url)';
  }
}

/// @nodoc
abstract mixin class _$ANImageModelCopyWith<$Res>
    implements $ANImageModelCopyWith<$Res> {
  factory _$ANImageModelCopyWith(
          _ANImageModel value, $Res Function(_ANImageModel) _then) =
      __$ANImageModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id, @JsonKey(name: 'filename') String fileName, String url});
}

/// @nodoc
class __$ANImageModelCopyWithImpl<$Res>
    implements _$ANImageModelCopyWith<$Res> {
  __$ANImageModelCopyWithImpl(this._self, this._then);

  final _ANImageModel _self;
  final $Res Function(_ANImageModel) _then;

  /// Create a copy of ANImageModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? fileName = null,
    Object? url = null,
  }) {
    return _then(_ANImageModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      fileName: null == fileName
          ? _self.fileName
          : fileName // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _self.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
