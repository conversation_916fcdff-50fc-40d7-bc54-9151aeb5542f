// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'image.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ANImageModel _$ANImageModelFromJson(Map<String, dynamic> json) {
  return _ANImageModel.fromJson(json);
}

/// @nodoc
mixin _$ANImageModel {
  String get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'filename')
  String get fileName => throw _privateConstructorUsedError;
  String get url => throw _privateConstructorUsedError;

  /// Serializes this ANImageModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ANImageModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ANImageModelCopyWith<ANImageModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANImageModelCopyWith<$Res> {
  factory $ANImageModelCopyWith(
          ANImageModel value, $Res Function(ANImageModel) then) =
      _$ANImageModelCopyWithImpl<$Res, ANImageModel>;
  @useResult
  $Res call(
      {String id, @JsonKey(name: 'filename') String fileName, String url});
}

/// @nodoc
class _$ANImageModelCopyWithImpl<$Res, $Val extends ANImageModel>
    implements $ANImageModelCopyWith<$Res> {
  _$ANImageModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ANImageModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? fileName = null,
    Object? url = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      fileName: null == fileName
          ? _value.fileName
          : fileName // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ANImageModelImplCopyWith<$Res>
    implements $ANImageModelCopyWith<$Res> {
  factory _$$ANImageModelImplCopyWith(
          _$ANImageModelImpl value, $Res Function(_$ANImageModelImpl) then) =
      __$$ANImageModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id, @JsonKey(name: 'filename') String fileName, String url});
}

/// @nodoc
class __$$ANImageModelImplCopyWithImpl<$Res>
    extends _$ANImageModelCopyWithImpl<$Res, _$ANImageModelImpl>
    implements _$$ANImageModelImplCopyWith<$Res> {
  __$$ANImageModelImplCopyWithImpl(
      _$ANImageModelImpl _value, $Res Function(_$ANImageModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ANImageModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? fileName = null,
    Object? url = null,
  }) {
    return _then(_$ANImageModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      fileName: null == fileName
          ? _value.fileName
          : fileName // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ANImageModelImpl implements _ANImageModel {
  const _$ANImageModelImpl(
      {required this.id,
      @JsonKey(name: 'filename') required this.fileName,
      required this.url});

  factory _$ANImageModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ANImageModelImplFromJson(json);

  @override
  final String id;
  @override
  @JsonKey(name: 'filename')
  final String fileName;
  @override
  final String url;

  @override
  String toString() {
    return 'ANImageModel(id: $id, fileName: $fileName, url: $url)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ANImageModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.fileName, fileName) ||
                other.fileName == fileName) &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, fileName, url);

  /// Create a copy of ANImageModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ANImageModelImplCopyWith<_$ANImageModelImpl> get copyWith =>
      __$$ANImageModelImplCopyWithImpl<_$ANImageModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ANImageModelImplToJson(
      this,
    );
  }
}

abstract class _ANImageModel implements ANImageModel {
  const factory _ANImageModel(
      {required final String id,
      @JsonKey(name: 'filename') required final String fileName,
      required final String url}) = _$ANImageModelImpl;

  factory _ANImageModel.fromJson(Map<String, dynamic> json) =
      _$ANImageModelImpl.fromJson;

  @override
  String get id;
  @override
  @JsonKey(name: 'filename')
  String get fileName;
  @override
  String get url;

  /// Create a copy of ANImageModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ANImageModelImplCopyWith<_$ANImageModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
