// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'schedule.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ANScheduleModel _$ANScheduleModelFromJson(Map<String, dynamic> json) {
  return _ANScheduleModel.fromJson(json);
}

/// @nodoc
mixin _$ANScheduleModel {
  String get id => throw _privateConstructorUsedError;
  Weekday get day => throw _privateConstructorUsedError;
  List<ANSlotsModel> get slots => throw _privateConstructorUsedError;

  /// Serializes this ANScheduleModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ANScheduleModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ANScheduleModelCopyWith<ANScheduleModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANScheduleModelCopyWith<$Res> {
  factory $ANScheduleModelCopyWith(
          ANScheduleModel value, $Res Function(ANScheduleModel) then) =
      _$ANScheduleModelCopyWithImpl<$Res, ANScheduleModel>;
  @useResult
  $Res call({String id, Weekday day, List<ANSlotsModel> slots});
}

/// @nodoc
class _$ANScheduleModelCopyWithImpl<$Res, $Val extends ANScheduleModel>
    implements $ANScheduleModelCopyWith<$Res> {
  _$ANScheduleModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ANScheduleModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? day = null,
    Object? slots = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      day: null == day
          ? _value.day
          : day // ignore: cast_nullable_to_non_nullable
              as Weekday,
      slots: null == slots
          ? _value.slots
          : slots // ignore: cast_nullable_to_non_nullable
              as List<ANSlotsModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ANScheduleModelImplCopyWith<$Res>
    implements $ANScheduleModelCopyWith<$Res> {
  factory _$$ANScheduleModelImplCopyWith(_$ANScheduleModelImpl value,
          $Res Function(_$ANScheduleModelImpl) then) =
      __$$ANScheduleModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, Weekday day, List<ANSlotsModel> slots});
}

/// @nodoc
class __$$ANScheduleModelImplCopyWithImpl<$Res>
    extends _$ANScheduleModelCopyWithImpl<$Res, _$ANScheduleModelImpl>
    implements _$$ANScheduleModelImplCopyWith<$Res> {
  __$$ANScheduleModelImplCopyWithImpl(
      _$ANScheduleModelImpl _value, $Res Function(_$ANScheduleModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ANScheduleModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? day = null,
    Object? slots = null,
  }) {
    return _then(_$ANScheduleModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      day: null == day
          ? _value.day
          : day // ignore: cast_nullable_to_non_nullable
              as Weekday,
      slots: null == slots
          ? _value._slots
          : slots // ignore: cast_nullable_to_non_nullable
              as List<ANSlotsModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ANScheduleModelImpl implements _ANScheduleModel {
  _$ANScheduleModelImpl(
      {required this.id,
      required this.day,
      required final List<ANSlotsModel> slots})
      : _slots = slots;

  factory _$ANScheduleModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ANScheduleModelImplFromJson(json);

  @override
  final String id;
  @override
  final Weekday day;
  final List<ANSlotsModel> _slots;
  @override
  List<ANSlotsModel> get slots {
    if (_slots is EqualUnmodifiableListView) return _slots;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_slots);
  }

  @override
  String toString() {
    return 'ANScheduleModel(id: $id, day: $day, slots: $slots)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ANScheduleModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.day, day) || other.day == day) &&
            const DeepCollectionEquality().equals(other._slots, _slots));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, day, const DeepCollectionEquality().hash(_slots));

  /// Create a copy of ANScheduleModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ANScheduleModelImplCopyWith<_$ANScheduleModelImpl> get copyWith =>
      __$$ANScheduleModelImplCopyWithImpl<_$ANScheduleModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ANScheduleModelImplToJson(
      this,
    );
  }
}

abstract class _ANScheduleModel implements ANScheduleModel {
  factory _ANScheduleModel(
      {required final String id,
      required final Weekday day,
      required final List<ANSlotsModel> slots}) = _$ANScheduleModelImpl;

  factory _ANScheduleModel.fromJson(Map<String, dynamic> json) =
      _$ANScheduleModelImpl.fromJson;

  @override
  String get id;
  @override
  Weekday get day;
  @override
  List<ANSlotsModel> get slots;

  /// Create a copy of ANScheduleModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ANScheduleModelImplCopyWith<_$ANScheduleModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
