// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'schedule.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ANScheduleModel {
  String get id;
  Weekday get day;
  List<ANSlotsModel> get slots;

  /// Create a copy of ANScheduleModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ANScheduleModelCopyWith<ANScheduleModel> get copyWith =>
      _$ANScheduleModelCopyWithImpl<ANScheduleModel>(
          this as ANScheduleModel, _$identity);

  /// Serializes this ANScheduleModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ANScheduleModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.day, day) || other.day == day) &&
            const DeepCollectionEquality().equals(other.slots, slots));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, day, const DeepCollectionEquality().hash(slots));

  @override
  String toString() {
    return 'ANScheduleModel(id: $id, day: $day, slots: $slots)';
  }
}

/// @nodoc
abstract mixin class $ANScheduleModelCopyWith<$Res> {
  factory $ANScheduleModelCopyWith(
          ANScheduleModel value, $Res Function(ANScheduleModel) _then) =
      _$ANScheduleModelCopyWithImpl;
  @useResult
  $Res call({String id, Weekday day, List<ANSlotsModel> slots});
}

/// @nodoc
class _$ANScheduleModelCopyWithImpl<$Res>
    implements $ANScheduleModelCopyWith<$Res> {
  _$ANScheduleModelCopyWithImpl(this._self, this._then);

  final ANScheduleModel _self;
  final $Res Function(ANScheduleModel) _then;

  /// Create a copy of ANScheduleModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? day = null,
    Object? slots = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      day: null == day
          ? _self.day
          : day // ignore: cast_nullable_to_non_nullable
              as Weekday,
      slots: null == slots
          ? _self.slots
          : slots // ignore: cast_nullable_to_non_nullable
              as List<ANSlotsModel>,
    ));
  }
}

/// Adds pattern-matching-related methods to [ANScheduleModel].
extension ANScheduleModelPatterns on ANScheduleModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ANScheduleModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANScheduleModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ANScheduleModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANScheduleModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ANScheduleModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANScheduleModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String id, Weekday day, List<ANSlotsModel> slots)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANScheduleModel() when $default != null:
        return $default(_that.id, _that.day, _that.slots);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String id, Weekday day, List<ANSlotsModel> slots) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANScheduleModel():
        return $default(_that.id, _that.day, _that.slots);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String id, Weekday day, List<ANSlotsModel> slots)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANScheduleModel() when $default != null:
        return $default(_that.id, _that.day, _that.slots);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ANScheduleModel implements ANScheduleModel {
  _ANScheduleModel(
      {required this.id,
      required this.day,
      required final List<ANSlotsModel> slots})
      : _slots = slots;
  factory _ANScheduleModel.fromJson(Map<String, dynamic> json) =>
      _$ANScheduleModelFromJson(json);

  @override
  final String id;
  @override
  final Weekday day;
  final List<ANSlotsModel> _slots;
  @override
  List<ANSlotsModel> get slots {
    if (_slots is EqualUnmodifiableListView) return _slots;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_slots);
  }

  /// Create a copy of ANScheduleModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ANScheduleModelCopyWith<_ANScheduleModel> get copyWith =>
      __$ANScheduleModelCopyWithImpl<_ANScheduleModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ANScheduleModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ANScheduleModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.day, day) || other.day == day) &&
            const DeepCollectionEquality().equals(other._slots, _slots));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, day, const DeepCollectionEquality().hash(_slots));

  @override
  String toString() {
    return 'ANScheduleModel(id: $id, day: $day, slots: $slots)';
  }
}

/// @nodoc
abstract mixin class _$ANScheduleModelCopyWith<$Res>
    implements $ANScheduleModelCopyWith<$Res> {
  factory _$ANScheduleModelCopyWith(
          _ANScheduleModel value, $Res Function(_ANScheduleModel) _then) =
      __$ANScheduleModelCopyWithImpl;
  @override
  @useResult
  $Res call({String id, Weekday day, List<ANSlotsModel> slots});
}

/// @nodoc
class __$ANScheduleModelCopyWithImpl<$Res>
    implements _$ANScheduleModelCopyWith<$Res> {
  __$ANScheduleModelCopyWithImpl(this._self, this._then);

  final _ANScheduleModel _self;
  final $Res Function(_ANScheduleModel) _then;

  /// Create a copy of ANScheduleModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? day = null,
    Object? slots = null,
  }) {
    return _then(_ANScheduleModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      day: null == day
          ? _self.day
          : day // ignore: cast_nullable_to_non_nullable
              as Weekday,
      slots: null == slots
          ? _self._slots
          : slots // ignore: cast_nullable_to_non_nullable
              as List<ANSlotsModel>,
    ));
  }
}

// dart format on
