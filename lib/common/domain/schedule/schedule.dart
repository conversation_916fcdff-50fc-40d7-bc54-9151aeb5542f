import 'package:ajmal_now_doctor/common/domain/slots/slots.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'schedule.freezed.dart';

enum Weekday {
  saturday,
  sunday,
  monday,
  tuesday,
  wednesday,
  thursday,
  friday;

  int weekday() {
    switch (this) {
      case Weekday.saturday:
        return DateTime.saturday;
      case Weekday.sunday:
        return DateTime.sunday;
      case Weekday.monday:
        return DateTime.monday;
      case Weekday.tuesday:
        return DateTime.tuesday;
      case Weekday.wednesday:
        return DateTime.wednesday;
      case Weekday.thursday:
        return DateTime.thursday;
      case Weekday.friday:
        return DateTime.friday;
    }
  }
}

@freezed
class ANScheduleModel with _$ANScheduleModel {
  factory ANScheduleModel({
    required String id,
    required Weekday day,
    required List<ANSlotsModel> slots,
  }) = _ANScheduleModel;

  factory ANScheduleModel.fromJson(Map<String, Object?> json) =>
      _$ANScheduleModel<PERSON><PERSON><PERSON><PERSON>(json);
}

/*
{
    "day": "saturday",
    "slots": [
        {
            "starts": "2022-12-29T07:01:00.000Z",
            "ends": "2022-12-29T08:00:00.000Z",
            "id": "63ada7553fbe855ec1a80b0e"
        }
    ],
    "id": "63ada7523fbe855ec1a80b0d"
}
*/
