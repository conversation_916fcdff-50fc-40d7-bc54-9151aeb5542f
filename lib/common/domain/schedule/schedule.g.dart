// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ANScheduleModel _$ANScheduleModelFromJson(Map<String, dynamic> json) =>
    _ANScheduleModel(
      id: json['id'] as String,
      day: $enumDecode(_$WeekdayEnumMap, json['day']),
      slots: (json['slots'] as List<dynamic>)
          .map((e) => ANSlotsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ANScheduleModelToJson(_ANScheduleModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'day': _$WeekdayEnumMap[instance.day]!,
      'slots': instance.slots,
    };

const _$WeekdayEnumMap = {
  Weekday.saturday: 'saturday',
  Weekday.sunday: 'sunday',
  Weekday.monday: 'monday',
  Weekday.tuesday: 'tuesday',
  Weekday.wednesday: 'wednesday',
  Weekday.thursday: 'thursday',
  Weekday.friday: 'friday',
};
