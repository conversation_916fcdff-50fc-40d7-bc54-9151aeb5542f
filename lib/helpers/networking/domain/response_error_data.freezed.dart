// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'response_error_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ANResponseErrorDataModel _$ANResponseErrorDataModelFromJson(
    Map<String, dynamic> json) {
  return _ANResponseErrorDataModel.fromJson(json);
}

/// @nodoc
mixin _$ANResponseErrorDataModel {
  String get message => throw _privateConstructorUsedError;
  String get field => throw _privateConstructorUsedError;

  /// Serializes this ANResponseErrorDataModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ANResponseErrorDataModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ANResponseErrorDataModelCopyWith<ANResponseErrorDataModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANResponseErrorDataModelCopyWith<$Res> {
  factory $ANResponseErrorDataModelCopyWith(ANResponseErrorDataModel value,
          $Res Function(ANResponseErrorDataModel) then) =
      _$ANResponseErrorDataModelCopyWithImpl<$Res, ANResponseErrorDataModel>;
  @useResult
  $Res call({String message, String field});
}

/// @nodoc
class _$ANResponseErrorDataModelCopyWithImpl<$Res,
        $Val extends ANResponseErrorDataModel>
    implements $ANResponseErrorDataModelCopyWith<$Res> {
  _$ANResponseErrorDataModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ANResponseErrorDataModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? field = null,
  }) {
    return _then(_value.copyWith(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      field: null == field
          ? _value.field
          : field // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ANResponseErrorDataModelImplCopyWith<$Res>
    implements $ANResponseErrorDataModelCopyWith<$Res> {
  factory _$$ANResponseErrorDataModelImplCopyWith(
          _$ANResponseErrorDataModelImpl value,
          $Res Function(_$ANResponseErrorDataModelImpl) then) =
      __$$ANResponseErrorDataModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String field});
}

/// @nodoc
class __$$ANResponseErrorDataModelImplCopyWithImpl<$Res>
    extends _$ANResponseErrorDataModelCopyWithImpl<$Res,
        _$ANResponseErrorDataModelImpl>
    implements _$$ANResponseErrorDataModelImplCopyWith<$Res> {
  __$$ANResponseErrorDataModelImplCopyWithImpl(
      _$ANResponseErrorDataModelImpl _value,
      $Res Function(_$ANResponseErrorDataModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ANResponseErrorDataModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? field = null,
  }) {
    return _then(_$ANResponseErrorDataModelImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      field: null == field
          ? _value.field
          : field // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ANResponseErrorDataModelImpl implements _ANResponseErrorDataModel {
  const _$ANResponseErrorDataModelImpl(
      {required this.message, required this.field});

  factory _$ANResponseErrorDataModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ANResponseErrorDataModelImplFromJson(json);

  @override
  final String message;
  @override
  final String field;

  @override
  String toString() {
    return 'ANResponseErrorDataModel(message: $message, field: $field)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ANResponseErrorDataModelImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.field, field) || other.field == field));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, message, field);

  /// Create a copy of ANResponseErrorDataModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ANResponseErrorDataModelImplCopyWith<_$ANResponseErrorDataModelImpl>
      get copyWith => __$$ANResponseErrorDataModelImplCopyWithImpl<
          _$ANResponseErrorDataModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ANResponseErrorDataModelImplToJson(
      this,
    );
  }
}

abstract class _ANResponseErrorDataModel implements ANResponseErrorDataModel {
  const factory _ANResponseErrorDataModel(
      {required final String message,
      required final String field}) = _$ANResponseErrorDataModelImpl;

  factory _ANResponseErrorDataModel.fromJson(Map<String, dynamic> json) =
      _$ANResponseErrorDataModelImpl.fromJson;

  @override
  String get message;
  @override
  String get field;

  /// Create a copy of ANResponseErrorDataModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ANResponseErrorDataModelImplCopyWith<_$ANResponseErrorDataModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
