// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'response_error_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ANResponseErrorDataModel {
  String get message;
  String get field;

  /// Create a copy of ANResponseErrorDataModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ANResponseErrorDataModelCopyWith<ANResponseErrorDataModel> get copyWith =>
      _$ANResponseErrorDataModelCopyWithImpl<ANResponseErrorDataModel>(
          this as ANResponseErrorDataModel, _$identity);

  /// Serializes this ANResponseErrorDataModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ANResponseErrorDataModel &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.field, field) || other.field == field));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, message, field);

  @override
  String toString() {
    return 'ANResponseErrorDataModel(message: $message, field: $field)';
  }
}

/// @nodoc
abstract mixin class $ANResponseErrorDataModelCopyWith<$Res> {
  factory $ANResponseErrorDataModelCopyWith(ANResponseErrorDataModel value,
          $Res Function(ANResponseErrorDataModel) _then) =
      _$ANResponseErrorDataModelCopyWithImpl;
  @useResult
  $Res call({String message, String field});
}

/// @nodoc
class _$ANResponseErrorDataModelCopyWithImpl<$Res>
    implements $ANResponseErrorDataModelCopyWith<$Res> {
  _$ANResponseErrorDataModelCopyWithImpl(this._self, this._then);

  final ANResponseErrorDataModel _self;
  final $Res Function(ANResponseErrorDataModel) _then;

  /// Create a copy of ANResponseErrorDataModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? field = null,
  }) {
    return _then(_self.copyWith(
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      field: null == field
          ? _self.field
          : field // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [ANResponseErrorDataModel].
extension ANResponseErrorDataModelPatterns on ANResponseErrorDataModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ANResponseErrorDataModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANResponseErrorDataModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ANResponseErrorDataModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANResponseErrorDataModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ANResponseErrorDataModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANResponseErrorDataModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String message, String field)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANResponseErrorDataModel() when $default != null:
        return $default(_that.message, _that.field);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String message, String field) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANResponseErrorDataModel():
        return $default(_that.message, _that.field);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String message, String field)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANResponseErrorDataModel() when $default != null:
        return $default(_that.message, _that.field);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ANResponseErrorDataModel implements ANResponseErrorDataModel {
  const _ANResponseErrorDataModel({required this.message, required this.field});
  factory _ANResponseErrorDataModel.fromJson(Map<String, dynamic> json) =>
      _$ANResponseErrorDataModelFromJson(json);

  @override
  final String message;
  @override
  final String field;

  /// Create a copy of ANResponseErrorDataModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ANResponseErrorDataModelCopyWith<_ANResponseErrorDataModel> get copyWith =>
      __$ANResponseErrorDataModelCopyWithImpl<_ANResponseErrorDataModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ANResponseErrorDataModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ANResponseErrorDataModel &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.field, field) || other.field == field));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, message, field);

  @override
  String toString() {
    return 'ANResponseErrorDataModel(message: $message, field: $field)';
  }
}

/// @nodoc
abstract mixin class _$ANResponseErrorDataModelCopyWith<$Res>
    implements $ANResponseErrorDataModelCopyWith<$Res> {
  factory _$ANResponseErrorDataModelCopyWith(_ANResponseErrorDataModel value,
          $Res Function(_ANResponseErrorDataModel) _then) =
      __$ANResponseErrorDataModelCopyWithImpl;
  @override
  @useResult
  $Res call({String message, String field});
}

/// @nodoc
class __$ANResponseErrorDataModelCopyWithImpl<$Res>
    implements _$ANResponseErrorDataModelCopyWith<$Res> {
  __$ANResponseErrorDataModelCopyWithImpl(this._self, this._then);

  final _ANResponseErrorDataModel _self;
  final $Res Function(_ANResponseErrorDataModel) _then;

  /// Create a copy of ANResponseErrorDataModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
    Object? field = null,
  }) {
    return _then(_ANResponseErrorDataModel(
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      field: null == field
          ? _self.field
          : field // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
