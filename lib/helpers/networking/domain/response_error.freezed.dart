// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'response_error.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ANResponseErrorModel {
  String get name;
  String get message;
  List<ANResponseErrorDataModel> get data;

  /// Create a copy of ANResponseErrorModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ANResponseErrorModelCopyWith<ANResponseErrorModel> get copyWith =>
      _$ANResponseErrorModelCopyWithImpl<ANResponseErrorModel>(
          this as ANResponseErrorModel, _$identity);

  /// Serializes this ANResponseErrorModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ANResponseErrorModel &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(other.data, data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, name, message, const DeepCollectionEquality().hash(data));

  @override
  String toString() {
    return 'ANResponseErrorModel(name: $name, message: $message, data: $data)';
  }
}

/// @nodoc
abstract mixin class $ANResponseErrorModelCopyWith<$Res> {
  factory $ANResponseErrorModelCopyWith(ANResponseErrorModel value,
          $Res Function(ANResponseErrorModel) _then) =
      _$ANResponseErrorModelCopyWithImpl;
  @useResult
  $Res call({String name, String message, List<ANResponseErrorDataModel> data});
}

/// @nodoc
class _$ANResponseErrorModelCopyWithImpl<$Res>
    implements $ANResponseErrorModelCopyWith<$Res> {
  _$ANResponseErrorModelCopyWithImpl(this._self, this._then);

  final ANResponseErrorModel _self;
  final $Res Function(ANResponseErrorModel) _then;

  /// Create a copy of ANResponseErrorModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? message = null,
    Object? data = null,
  }) {
    return _then(_self.copyWith(
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      data: null == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ANResponseErrorDataModel>,
    ));
  }
}

/// Adds pattern-matching-related methods to [ANResponseErrorModel].
extension ANResponseErrorModelPatterns on ANResponseErrorModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ANResponseErrorModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANResponseErrorModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ANResponseErrorModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANResponseErrorModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ANResponseErrorModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANResponseErrorModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String name, String message, List<ANResponseErrorDataModel> data)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANResponseErrorModel() when $default != null:
        return $default(_that.name, _that.message, _that.data);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String name, String message, List<ANResponseErrorDataModel> data)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANResponseErrorModel():
        return $default(_that.name, _that.message, _that.data);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String name, String message, List<ANResponseErrorDataModel> data)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANResponseErrorModel() when $default != null:
        return $default(_that.name, _that.message, _that.data);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ANResponseErrorModel implements ANResponseErrorModel {
  const _ANResponseErrorModel(
      {required this.name,
      required this.message,
      required final List<ANResponseErrorDataModel> data})
      : _data = data;
  factory _ANResponseErrorModel.fromJson(Map<String, dynamic> json) =>
      _$ANResponseErrorModelFromJson(json);

  @override
  final String name;
  @override
  final String message;
  final List<ANResponseErrorDataModel> _data;
  @override
  List<ANResponseErrorDataModel> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  /// Create a copy of ANResponseErrorModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ANResponseErrorModelCopyWith<_ANResponseErrorModel> get copyWith =>
      __$ANResponseErrorModelCopyWithImpl<_ANResponseErrorModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ANResponseErrorModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ANResponseErrorModel &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, name, message, const DeepCollectionEquality().hash(_data));

  @override
  String toString() {
    return 'ANResponseErrorModel(name: $name, message: $message, data: $data)';
  }
}

/// @nodoc
abstract mixin class _$ANResponseErrorModelCopyWith<$Res>
    implements $ANResponseErrorModelCopyWith<$Res> {
  factory _$ANResponseErrorModelCopyWith(_ANResponseErrorModel value,
          $Res Function(_ANResponseErrorModel) _then) =
      __$ANResponseErrorModelCopyWithImpl;
  @override
  @useResult
  $Res call({String name, String message, List<ANResponseErrorDataModel> data});
}

/// @nodoc
class __$ANResponseErrorModelCopyWithImpl<$Res>
    implements _$ANResponseErrorModelCopyWith<$Res> {
  __$ANResponseErrorModelCopyWithImpl(this._self, this._then);

  final _ANResponseErrorModel _self;
  final $Res Function(_ANResponseErrorModel) _then;

  /// Create a copy of ANResponseErrorModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? name = null,
    Object? message = null,
    Object? data = null,
  }) {
    return _then(_ANResponseErrorModel(
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      data: null == data
          ? _self._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ANResponseErrorDataModel>,
    ));
  }
}

// dart format on
