// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'response_error.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ANResponseErrorModel _$ANResponseErrorModelFromJson(Map<String, dynamic> json) {
  return _ANResponseErrorModel.fromJson(json);
}

/// @nodoc
mixin _$ANResponseErrorModel {
  String get name => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  List<ANResponseErrorDataModel> get data => throw _privateConstructorUsedError;

  /// Serializes this ANResponseErrorModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ANResponseErrorModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ANResponseErrorModelCopyWith<ANResponseErrorModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANResponseErrorModelCopyWith<$Res> {
  factory $ANResponseErrorModelCopyWith(ANResponseErrorModel value,
          $Res Function(ANResponseErrorModel) then) =
      _$ANResponseErrorModelCopyWithImpl<$Res, ANResponseErrorModel>;
  @useResult
  $Res call({String name, String message, List<ANResponseErrorDataModel> data});
}

/// @nodoc
class _$ANResponseErrorModelCopyWithImpl<$Res,
        $Val extends ANResponseErrorModel>
    implements $ANResponseErrorModelCopyWith<$Res> {
  _$ANResponseErrorModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ANResponseErrorModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? message = null,
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ANResponseErrorDataModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ANResponseErrorModelImplCopyWith<$Res>
    implements $ANResponseErrorModelCopyWith<$Res> {
  factory _$$ANResponseErrorModelImplCopyWith(_$ANResponseErrorModelImpl value,
          $Res Function(_$ANResponseErrorModelImpl) then) =
      __$$ANResponseErrorModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name, String message, List<ANResponseErrorDataModel> data});
}

/// @nodoc
class __$$ANResponseErrorModelImplCopyWithImpl<$Res>
    extends _$ANResponseErrorModelCopyWithImpl<$Res, _$ANResponseErrorModelImpl>
    implements _$$ANResponseErrorModelImplCopyWith<$Res> {
  __$$ANResponseErrorModelImplCopyWithImpl(_$ANResponseErrorModelImpl _value,
      $Res Function(_$ANResponseErrorModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ANResponseErrorModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? message = null,
    Object? data = null,
  }) {
    return _then(_$ANResponseErrorModelImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ANResponseErrorDataModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ANResponseErrorModelImpl implements _ANResponseErrorModel {
  const _$ANResponseErrorModelImpl(
      {required this.name,
      required this.message,
      required final List<ANResponseErrorDataModel> data})
      : _data = data;

  factory _$ANResponseErrorModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ANResponseErrorModelImplFromJson(json);

  @override
  final String name;
  @override
  final String message;
  final List<ANResponseErrorDataModel> _data;
  @override
  List<ANResponseErrorDataModel> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString() {
    return 'ANResponseErrorModel(name: $name, message: $message, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ANResponseErrorModelImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, name, message, const DeepCollectionEquality().hash(_data));

  /// Create a copy of ANResponseErrorModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ANResponseErrorModelImplCopyWith<_$ANResponseErrorModelImpl>
      get copyWith =>
          __$$ANResponseErrorModelImplCopyWithImpl<_$ANResponseErrorModelImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ANResponseErrorModelImplToJson(
      this,
    );
  }
}

abstract class _ANResponseErrorModel implements ANResponseErrorModel {
  const factory _ANResponseErrorModel(
          {required final String name,
          required final String message,
          required final List<ANResponseErrorDataModel> data}) =
      _$ANResponseErrorModelImpl;

  factory _ANResponseErrorModel.fromJson(Map<String, dynamic> json) =
      _$ANResponseErrorModelImpl.fromJson;

  @override
  String get name;
  @override
  String get message;
  @override
  List<ANResponseErrorDataModel> get data;

  /// Create a copy of ANResponseErrorModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ANResponseErrorModelImplCopyWith<_$ANResponseErrorModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
