// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ANResponseModel {
  String? get message;
  int get statusCode;
  Map<String, dynamic>? get data; // dynamic doc,
// dynamic docs,
// dynamic sponsored,
// dynamic screens,
// dynamic terms,
// dynamic conditions,
// int? totalPages,
  @JsonKey(name: 'error')
  String? get errorMessage;
  @JsonKey(name: 'errors')
  List<ANResponseErrorModel>? get error;

  /// Create a copy of ANResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ANResponseModelCopyWith<ANResponseModel> get copyWith =>
      _$ANResponseModelCopyWithImpl<ANResponseModel>(
          this as ANResponseModel, _$identity);

  /// Serializes this ANResponseModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ANResponseModel &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.statusCode, statusCode) ||
                other.statusCode == statusCode) &&
            const DeepCollectionEquality().equals(other.data, data) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality().equals(other.error, error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      message,
      statusCode,
      const DeepCollectionEquality().hash(data),
      errorMessage,
      const DeepCollectionEquality().hash(error));

  @override
  String toString() {
    return 'ANResponseModel(message: $message, statusCode: $statusCode, data: $data, errorMessage: $errorMessage, error: $error)';
  }
}

/// @nodoc
abstract mixin class $ANResponseModelCopyWith<$Res> {
  factory $ANResponseModelCopyWith(
          ANResponseModel value, $Res Function(ANResponseModel) _then) =
      _$ANResponseModelCopyWithImpl;
  @useResult
  $Res call(
      {String? message,
      int statusCode,
      Map<String, dynamic>? data,
      @JsonKey(name: 'error') String? errorMessage,
      @JsonKey(name: 'errors') List<ANResponseErrorModel>? error});
}

/// @nodoc
class _$ANResponseModelCopyWithImpl<$Res>
    implements $ANResponseModelCopyWith<$Res> {
  _$ANResponseModelCopyWithImpl(this._self, this._then);

  final ANResponseModel _self;
  final $Res Function(ANResponseModel) _then;

  /// Create a copy of ANResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = freezed,
    Object? statusCode = null,
    Object? data = freezed,
    Object? errorMessage = freezed,
    Object? error = freezed,
  }) {
    return _then(_self.copyWith(
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCode: null == statusCode
          ? _self.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int,
      data: freezed == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as List<ANResponseErrorModel>?,
    ));
  }
}

/// Adds pattern-matching-related methods to [ANResponseModel].
extension ANResponseModelPatterns on ANResponseModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ANResponseModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANResponseModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ANResponseModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANResponseModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ANResponseModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANResponseModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String? message,
            int statusCode,
            Map<String, dynamic>? data,
            @JsonKey(name: 'error') String? errorMessage,
            @JsonKey(name: 'errors') List<ANResponseErrorModel>? error)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANResponseModel() when $default != null:
        return $default(_that.message, _that.statusCode, _that.data,
            _that.errorMessage, _that.error);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String? message,
            int statusCode,
            Map<String, dynamic>? data,
            @JsonKey(name: 'error') String? errorMessage,
            @JsonKey(name: 'errors') List<ANResponseErrorModel>? error)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANResponseModel():
        return $default(_that.message, _that.statusCode, _that.data,
            _that.errorMessage, _that.error);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String? message,
            int statusCode,
            Map<String, dynamic>? data,
            @JsonKey(name: 'error') String? errorMessage,
            @JsonKey(name: 'errors') List<ANResponseErrorModel>? error)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANResponseModel() when $default != null:
        return $default(_that.message, _that.statusCode, _that.data,
            _that.errorMessage, _that.error);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ANResponseModel implements ANResponseModel {
  const _ANResponseModel(
      {this.message,
      required this.statusCode,
      final Map<String, dynamic>? data,
      @JsonKey(name: 'error') this.errorMessage,
      @JsonKey(name: 'errors') final List<ANResponseErrorModel>? error})
      : _data = data,
        _error = error;
  factory _ANResponseModel.fromJson(Map<String, dynamic> json) =>
      _$ANResponseModelFromJson(json);

  @override
  final String? message;
  @override
  final int statusCode;
  final Map<String, dynamic>? _data;
  @override
  Map<String, dynamic>? get data {
    final value = _data;
    if (value == null) return null;
    if (_data is EqualUnmodifiableMapView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

// dynamic doc,
// dynamic docs,
// dynamic sponsored,
// dynamic screens,
// dynamic terms,
// dynamic conditions,
// int? totalPages,
  @override
  @JsonKey(name: 'error')
  final String? errorMessage;
  final List<ANResponseErrorModel>? _error;
  @override
  @JsonKey(name: 'errors')
  List<ANResponseErrorModel>? get error {
    final value = _error;
    if (value == null) return null;
    if (_error is EqualUnmodifiableListView) return _error;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// Create a copy of ANResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ANResponseModelCopyWith<_ANResponseModel> get copyWith =>
      __$ANResponseModelCopyWithImpl<_ANResponseModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ANResponseModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ANResponseModel &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.statusCode, statusCode) ||
                other.statusCode == statusCode) &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality().equals(other._error, _error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      message,
      statusCode,
      const DeepCollectionEquality().hash(_data),
      errorMessage,
      const DeepCollectionEquality().hash(_error));

  @override
  String toString() {
    return 'ANResponseModel(message: $message, statusCode: $statusCode, data: $data, errorMessage: $errorMessage, error: $error)';
  }
}

/// @nodoc
abstract mixin class _$ANResponseModelCopyWith<$Res>
    implements $ANResponseModelCopyWith<$Res> {
  factory _$ANResponseModelCopyWith(
          _ANResponseModel value, $Res Function(_ANResponseModel) _then) =
      __$ANResponseModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? message,
      int statusCode,
      Map<String, dynamic>? data,
      @JsonKey(name: 'error') String? errorMessage,
      @JsonKey(name: 'errors') List<ANResponseErrorModel>? error});
}

/// @nodoc
class __$ANResponseModelCopyWithImpl<$Res>
    implements _$ANResponseModelCopyWith<$Res> {
  __$ANResponseModelCopyWithImpl(this._self, this._then);

  final _ANResponseModel _self;
  final $Res Function(_ANResponseModel) _then;

  /// Create a copy of ANResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = freezed,
    Object? statusCode = null,
    Object? data = freezed,
    Object? errorMessage = freezed,
    Object? error = freezed,
  }) {
    return _then(_ANResponseModel(
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCode: null == statusCode
          ? _self.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int,
      data: freezed == data
          ? _self._data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      error: freezed == error
          ? _self._error
          : error // ignore: cast_nullable_to_non_nullable
              as List<ANResponseErrorModel>?,
    ));
  }
}

// dart format on
