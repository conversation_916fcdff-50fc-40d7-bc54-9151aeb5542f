// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ANResponseModel _$ANResponseModelFromJson(Map<String, dynamic> json) {
  return _ANResponseModel.fromJson(json);
}

/// @nodoc
mixin _$ANResponseModel {
  String? get message => throw _privateConstructorUsedError;
  int get statusCode => throw _privateConstructorUsedError;
  Map<String, dynamic>? get data =>
      throw _privateConstructorUsedError; // dynamic doc,
// dynamic docs,
// dynamic sponsored,
// dynamic screens,
// dynamic terms,
// dynamic conditions,
// int? totalPages,
  @JsonKey(name: 'error')
  String? get errorMessage => throw _privateConstructorUsedError;
  @JsonKey(name: 'errors')
  List<ANResponseErrorModel>? get error => throw _privateConstructorUsedError;

  /// Serializes this ANResponseModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ANResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ANResponseModelCopyWith<ANResponseModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANResponseModelCopyWith<$Res> {
  factory $ANResponseModelCopyWith(
          ANResponseModel value, $Res Function(ANResponseModel) then) =
      _$ANResponseModelCopyWithImpl<$Res, ANResponseModel>;
  @useResult
  $Res call(
      {String? message,
      int statusCode,
      Map<String, dynamic>? data,
      @JsonKey(name: 'error') String? errorMessage,
      @JsonKey(name: 'errors') List<ANResponseErrorModel>? error});
}

/// @nodoc
class _$ANResponseModelCopyWithImpl<$Res, $Val extends ANResponseModel>
    implements $ANResponseModelCopyWith<$Res> {
  _$ANResponseModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ANResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = freezed,
    Object? statusCode = null,
    Object? data = freezed,
    Object? errorMessage = freezed,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCode: null == statusCode
          ? _value.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as List<ANResponseErrorModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ANResponseModelImplCopyWith<$Res>
    implements $ANResponseModelCopyWith<$Res> {
  factory _$$ANResponseModelImplCopyWith(_$ANResponseModelImpl value,
          $Res Function(_$ANResponseModelImpl) then) =
      __$$ANResponseModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? message,
      int statusCode,
      Map<String, dynamic>? data,
      @JsonKey(name: 'error') String? errorMessage,
      @JsonKey(name: 'errors') List<ANResponseErrorModel>? error});
}

/// @nodoc
class __$$ANResponseModelImplCopyWithImpl<$Res>
    extends _$ANResponseModelCopyWithImpl<$Res, _$ANResponseModelImpl>
    implements _$$ANResponseModelImplCopyWith<$Res> {
  __$$ANResponseModelImplCopyWithImpl(
      _$ANResponseModelImpl _value, $Res Function(_$ANResponseModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ANResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = freezed,
    Object? statusCode = null,
    Object? data = freezed,
    Object? errorMessage = freezed,
    Object? error = freezed,
  }) {
    return _then(_$ANResponseModelImpl(
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCode: null == statusCode
          ? _value.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int,
      data: freezed == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      error: freezed == error
          ? _value._error
          : error // ignore: cast_nullable_to_non_nullable
              as List<ANResponseErrorModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ANResponseModelImpl implements _ANResponseModel {
  const _$ANResponseModelImpl(
      {this.message,
      required this.statusCode,
      final Map<String, dynamic>? data,
      @JsonKey(name: 'error') this.errorMessage,
      @JsonKey(name: 'errors') final List<ANResponseErrorModel>? error})
      : _data = data,
        _error = error;

  factory _$ANResponseModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ANResponseModelImplFromJson(json);

  @override
  final String? message;
  @override
  final int statusCode;
  final Map<String, dynamic>? _data;
  @override
  Map<String, dynamic>? get data {
    final value = _data;
    if (value == null) return null;
    if (_data is EqualUnmodifiableMapView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

// dynamic doc,
// dynamic docs,
// dynamic sponsored,
// dynamic screens,
// dynamic terms,
// dynamic conditions,
// int? totalPages,
  @override
  @JsonKey(name: 'error')
  final String? errorMessage;
  final List<ANResponseErrorModel>? _error;
  @override
  @JsonKey(name: 'errors')
  List<ANResponseErrorModel>? get error {
    final value = _error;
    if (value == null) return null;
    if (_error is EqualUnmodifiableListView) return _error;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ANResponseModel(message: $message, statusCode: $statusCode, data: $data, errorMessage: $errorMessage, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ANResponseModelImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.statusCode, statusCode) ||
                other.statusCode == statusCode) &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality().equals(other._error, _error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      message,
      statusCode,
      const DeepCollectionEquality().hash(_data),
      errorMessage,
      const DeepCollectionEquality().hash(_error));

  /// Create a copy of ANResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ANResponseModelImplCopyWith<_$ANResponseModelImpl> get copyWith =>
      __$$ANResponseModelImplCopyWithImpl<_$ANResponseModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ANResponseModelImplToJson(
      this,
    );
  }
}

abstract class _ANResponseModel implements ANResponseModel {
  const factory _ANResponseModel(
          {final String? message,
          required final int statusCode,
          final Map<String, dynamic>? data,
          @JsonKey(name: 'error') final String? errorMessage,
          @JsonKey(name: 'errors') final List<ANResponseErrorModel>? error}) =
      _$ANResponseModelImpl;

  factory _ANResponseModel.fromJson(Map<String, dynamic> json) =
      _$ANResponseModelImpl.fromJson;

  @override
  String? get message;
  @override
  int get statusCode;
  @override
  Map<String, dynamic>? get data; // dynamic doc,
// dynamic docs,
// dynamic sponsored,
// dynamic screens,
// dynamic terms,
// dynamic conditions,
// int? totalPages,
  @override
  @JsonKey(name: 'error')
  String? get errorMessage;
  @override
  @JsonKey(name: 'errors')
  List<ANResponseErrorModel>? get error;

  /// Create a copy of ANResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ANResponseModelImplCopyWith<_$ANResponseModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
