// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_status.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ANPaymentStatusModel {
  @JsonKey(name: 'payment_status')
  HistoryStatus get paymentStatus;
  @JsonKey(name: 'paymob_refid')
  String? get paymobRefId;

  /// Create a copy of ANPaymentStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ANPaymentStatusModelCopyWith<ANPaymentStatusModel> get copyWith =>
      _$ANPaymentStatusModelCopyWithImpl<ANPaymentStatusModel>(
          this as ANPaymentStatusModel, _$identity);

  /// Serializes this ANPaymentStatusModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ANPaymentStatusModel &&
            (identical(other.paymentStatus, paymentStatus) ||
                other.paymentStatus == paymentStatus) &&
            (identical(other.paymobRefId, paymobRefId) ||
                other.paymobRefId == paymobRefId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, paymentStatus, paymobRefId);

  @override
  String toString() {
    return 'ANPaymentStatusModel(paymentStatus: $paymentStatus, paymobRefId: $paymobRefId)';
  }
}

/// @nodoc
abstract mixin class $ANPaymentStatusModelCopyWith<$Res> {
  factory $ANPaymentStatusModelCopyWith(ANPaymentStatusModel value,
          $Res Function(ANPaymentStatusModel) _then) =
      _$ANPaymentStatusModelCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'payment_status') HistoryStatus paymentStatus,
      @JsonKey(name: 'paymob_refid') String? paymobRefId});
}

/// @nodoc
class _$ANPaymentStatusModelCopyWithImpl<$Res>
    implements $ANPaymentStatusModelCopyWith<$Res> {
  _$ANPaymentStatusModelCopyWithImpl(this._self, this._then);

  final ANPaymentStatusModel _self;
  final $Res Function(ANPaymentStatusModel) _then;

  /// Create a copy of ANPaymentStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? paymentStatus = null,
    Object? paymobRefId = freezed,
  }) {
    return _then(_self.copyWith(
      paymentStatus: null == paymentStatus
          ? _self.paymentStatus
          : paymentStatus // ignore: cast_nullable_to_non_nullable
              as HistoryStatus,
      paymobRefId: freezed == paymobRefId
          ? _self.paymobRefId
          : paymobRefId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [ANPaymentStatusModel].
extension ANPaymentStatusModelPatterns on ANPaymentStatusModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ANPaymentStatusModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANPaymentStatusModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ANPaymentStatusModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANPaymentStatusModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ANPaymentStatusModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANPaymentStatusModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'payment_status') HistoryStatus paymentStatus,
            @JsonKey(name: 'paymob_refid') String? paymobRefId)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANPaymentStatusModel() when $default != null:
        return $default(_that.paymentStatus, _that.paymobRefId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            @JsonKey(name: 'payment_status') HistoryStatus paymentStatus,
            @JsonKey(name: 'paymob_refid') String? paymobRefId)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANPaymentStatusModel():
        return $default(_that.paymentStatus, _that.paymobRefId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            @JsonKey(name: 'payment_status') HistoryStatus paymentStatus,
            @JsonKey(name: 'paymob_refid') String? paymobRefId)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANPaymentStatusModel() when $default != null:
        return $default(_that.paymentStatus, _that.paymobRefId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ANPaymentStatusModel implements ANPaymentStatusModel {
  const _ANPaymentStatusModel(
      {@JsonKey(name: 'payment_status') required this.paymentStatus,
      @JsonKey(name: 'paymob_refid') this.paymobRefId});
  factory _ANPaymentStatusModel.fromJson(Map<String, dynamic> json) =>
      _$ANPaymentStatusModelFromJson(json);

  @override
  @JsonKey(name: 'payment_status')
  final HistoryStatus paymentStatus;
  @override
  @JsonKey(name: 'paymob_refid')
  final String? paymobRefId;

  /// Create a copy of ANPaymentStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ANPaymentStatusModelCopyWith<_ANPaymentStatusModel> get copyWith =>
      __$ANPaymentStatusModelCopyWithImpl<_ANPaymentStatusModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ANPaymentStatusModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ANPaymentStatusModel &&
            (identical(other.paymentStatus, paymentStatus) ||
                other.paymentStatus == paymentStatus) &&
            (identical(other.paymobRefId, paymobRefId) ||
                other.paymobRefId == paymobRefId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, paymentStatus, paymobRefId);

  @override
  String toString() {
    return 'ANPaymentStatusModel(paymentStatus: $paymentStatus, paymobRefId: $paymobRefId)';
  }
}

/// @nodoc
abstract mixin class _$ANPaymentStatusModelCopyWith<$Res>
    implements $ANPaymentStatusModelCopyWith<$Res> {
  factory _$ANPaymentStatusModelCopyWith(_ANPaymentStatusModel value,
          $Res Function(_ANPaymentStatusModel) _then) =
      __$ANPaymentStatusModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'payment_status') HistoryStatus paymentStatus,
      @JsonKey(name: 'paymob_refid') String? paymobRefId});
}

/// @nodoc
class __$ANPaymentStatusModelCopyWithImpl<$Res>
    implements _$ANPaymentStatusModelCopyWith<$Res> {
  __$ANPaymentStatusModelCopyWithImpl(this._self, this._then);

  final _ANPaymentStatusModel _self;
  final $Res Function(_ANPaymentStatusModel) _then;

  /// Create a copy of ANPaymentStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? paymentStatus = null,
    Object? paymobRefId = freezed,
  }) {
    return _then(_ANPaymentStatusModel(
      paymentStatus: null == paymentStatus
          ? _self.paymentStatus
          : paymentStatus // ignore: cast_nullable_to_non_nullable
              as HistoryStatus,
      paymobRefId: freezed == paymobRefId
          ? _self.paymobRefId
          : paymobRefId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
