// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_status.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ANPaymentStatusModel _$ANPaymentStatusModelFromJson(Map<String, dynamic> json) {
  return _ANPaymentStatusModel.fromJson(json);
}

/// @nodoc
mixin _$ANPaymentStatusModel {
  @JsonKey(name: 'payment_status')
  HistoryStatus get paymentStatus => throw _privateConstructorUsedError;
  @JsonKey(name: 'paymob_refid')
  String? get paymobRefId => throw _privateConstructorUsedError;

  /// Serializes this ANPaymentStatusModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ANPaymentStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ANPaymentStatusModelCopyWith<ANPaymentStatusModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANPaymentStatusModelCopyWith<$Res> {
  factory $ANPaymentStatusModelCopyWith(ANPaymentStatusModel value,
          $Res Function(ANPaymentStatusModel) then) =
      _$ANPaymentStatusModelCopyWithImpl<$Res, ANPaymentStatusModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'payment_status') HistoryStatus paymentStatus,
      @JsonKey(name: 'paymob_refid') String? paymobRefId});
}

/// @nodoc
class _$ANPaymentStatusModelCopyWithImpl<$Res,
        $Val extends ANPaymentStatusModel>
    implements $ANPaymentStatusModelCopyWith<$Res> {
  _$ANPaymentStatusModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ANPaymentStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? paymentStatus = null,
    Object? paymobRefId = freezed,
  }) {
    return _then(_value.copyWith(
      paymentStatus: null == paymentStatus
          ? _value.paymentStatus
          : paymentStatus // ignore: cast_nullable_to_non_nullable
              as HistoryStatus,
      paymobRefId: freezed == paymobRefId
          ? _value.paymobRefId
          : paymobRefId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ANPaymentStatusModelImplCopyWith<$Res>
    implements $ANPaymentStatusModelCopyWith<$Res> {
  factory _$$ANPaymentStatusModelImplCopyWith(_$ANPaymentStatusModelImpl value,
          $Res Function(_$ANPaymentStatusModelImpl) then) =
      __$$ANPaymentStatusModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'payment_status') HistoryStatus paymentStatus,
      @JsonKey(name: 'paymob_refid') String? paymobRefId});
}

/// @nodoc
class __$$ANPaymentStatusModelImplCopyWithImpl<$Res>
    extends _$ANPaymentStatusModelCopyWithImpl<$Res, _$ANPaymentStatusModelImpl>
    implements _$$ANPaymentStatusModelImplCopyWith<$Res> {
  __$$ANPaymentStatusModelImplCopyWithImpl(_$ANPaymentStatusModelImpl _value,
      $Res Function(_$ANPaymentStatusModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ANPaymentStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? paymentStatus = null,
    Object? paymobRefId = freezed,
  }) {
    return _then(_$ANPaymentStatusModelImpl(
      paymentStatus: null == paymentStatus
          ? _value.paymentStatus
          : paymentStatus // ignore: cast_nullable_to_non_nullable
              as HistoryStatus,
      paymobRefId: freezed == paymobRefId
          ? _value.paymobRefId
          : paymobRefId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ANPaymentStatusModelImpl implements _ANPaymentStatusModel {
  const _$ANPaymentStatusModelImpl(
      {@JsonKey(name: 'payment_status') required this.paymentStatus,
      @JsonKey(name: 'paymob_refid') this.paymobRefId});

  factory _$ANPaymentStatusModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ANPaymentStatusModelImplFromJson(json);

  @override
  @JsonKey(name: 'payment_status')
  final HistoryStatus paymentStatus;
  @override
  @JsonKey(name: 'paymob_refid')
  final String? paymobRefId;

  @override
  String toString() {
    return 'ANPaymentStatusModel(paymentStatus: $paymentStatus, paymobRefId: $paymobRefId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ANPaymentStatusModelImpl &&
            (identical(other.paymentStatus, paymentStatus) ||
                other.paymentStatus == paymentStatus) &&
            (identical(other.paymobRefId, paymobRefId) ||
                other.paymobRefId == paymobRefId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, paymentStatus, paymobRefId);

  /// Create a copy of ANPaymentStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ANPaymentStatusModelImplCopyWith<_$ANPaymentStatusModelImpl>
      get copyWith =>
          __$$ANPaymentStatusModelImplCopyWithImpl<_$ANPaymentStatusModelImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ANPaymentStatusModelImplToJson(
      this,
    );
  }
}

abstract class _ANPaymentStatusModel implements ANPaymentStatusModel {
  const factory _ANPaymentStatusModel(
          {@JsonKey(name: 'payment_status')
          required final HistoryStatus paymentStatus,
          @JsonKey(name: 'paymob_refid') final String? paymobRefId}) =
      _$ANPaymentStatusModelImpl;

  factory _ANPaymentStatusModel.fromJson(Map<String, dynamic> json) =
      _$ANPaymentStatusModelImpl.fromJson;

  @override
  @JsonKey(name: 'payment_status')
  HistoryStatus get paymentStatus;
  @override
  @JsonKey(name: 'paymob_refid')
  String? get paymobRefId;

  /// Create a copy of ANPaymentStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ANPaymentStatusModelImplCopyWith<_$ANPaymentStatusModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
