// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_status.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ANPaymentStatusModelImpl _$$ANPaymentStatusModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ANPaymentStatusModelImpl(
      paymentStatus:
          $enumDecode(_$HistoryStatusEnumMap, json['payment_status']),
      paymobRefId: json['paymob_refid'] as String?,
    );

Map<String, dynamic> _$$ANPaymentStatusModelImplToJson(
        _$ANPaymentStatusModelImpl instance) =>
    <String, dynamic>{
      'payment_status': _$HistoryStatusEnumMap[instance.paymentStatus]!,
      'paymob_refid': instance.paymobRefId,
    };

const _$HistoryStatusEnumMap = {
  HistoryStatus.paid: 'paid',
  HistoryStatus.complete: 'complete',
  HistoryStatus.pending: 'pending',
  HistoryStatus.blocked: 'blocked',
};
