// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'onboarding.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ANOnboardingModel _$ANOnboardingModelFromJson(Map<String, dynamic> json) {
  return _ANOnboardingModel.fromJson(json);
}

/// @nodoc
mixin _$ANOnboardingModel {
  String get id => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  ANImageModel get image => throw _privateConstructorUsedError;

  /// Serializes this ANOnboardingModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ANOnboardingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ANOnboardingModelCopyWith<ANOnboardingModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANOnboardingModelCopyWith<$Res> {
  factory $ANOnboardingModelCopyWith(
          ANOnboardingModel value, $Res Function(ANOnboardingModel) then) =
      _$ANOnboardingModelCopyWithImpl<$Res, ANOnboardingModel>;
  @useResult
  $Res call(
      {String id, String? title, String? description, ANImageModel image});

  $ANImageModelCopyWith<$Res> get image;
}

/// @nodoc
class _$ANOnboardingModelCopyWithImpl<$Res, $Val extends ANOnboardingModel>
    implements $ANOnboardingModelCopyWith<$Res> {
  _$ANOnboardingModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ANOnboardingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = freezed,
    Object? description = freezed,
    Object? image = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      image: null == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as ANImageModel,
    ) as $Val);
  }

  /// Create a copy of ANOnboardingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ANImageModelCopyWith<$Res> get image {
    return $ANImageModelCopyWith<$Res>(_value.image, (value) {
      return _then(_value.copyWith(image: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ANOnboardingModelImplCopyWith<$Res>
    implements $ANOnboardingModelCopyWith<$Res> {
  factory _$$ANOnboardingModelImplCopyWith(_$ANOnboardingModelImpl value,
          $Res Function(_$ANOnboardingModelImpl) then) =
      __$$ANOnboardingModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id, String? title, String? description, ANImageModel image});

  @override
  $ANImageModelCopyWith<$Res> get image;
}

/// @nodoc
class __$$ANOnboardingModelImplCopyWithImpl<$Res>
    extends _$ANOnboardingModelCopyWithImpl<$Res, _$ANOnboardingModelImpl>
    implements _$$ANOnboardingModelImplCopyWith<$Res> {
  __$$ANOnboardingModelImplCopyWithImpl(_$ANOnboardingModelImpl _value,
      $Res Function(_$ANOnboardingModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ANOnboardingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = freezed,
    Object? description = freezed,
    Object? image = null,
  }) {
    return _then(_$ANOnboardingModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      image: null == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as ANImageModel,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ANOnboardingModelImpl implements _ANOnboardingModel {
  const _$ANOnboardingModelImpl(
      {required this.id, this.title, this.description, required this.image});

  factory _$ANOnboardingModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ANOnboardingModelImplFromJson(json);

  @override
  final String id;
  @override
  final String? title;
  @override
  final String? description;
  @override
  final ANImageModel image;

  @override
  String toString() {
    return 'ANOnboardingModel(id: $id, title: $title, description: $description, image: $image)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ANOnboardingModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.image, image) || other.image == image));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, title, description, image);

  /// Create a copy of ANOnboardingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ANOnboardingModelImplCopyWith<_$ANOnboardingModelImpl> get copyWith =>
      __$$ANOnboardingModelImplCopyWithImpl<_$ANOnboardingModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ANOnboardingModelImplToJson(
      this,
    );
  }
}

abstract class _ANOnboardingModel implements ANOnboardingModel {
  const factory _ANOnboardingModel(
      {required final String id,
      final String? title,
      final String? description,
      required final ANImageModel image}) = _$ANOnboardingModelImpl;

  factory _ANOnboardingModel.fromJson(Map<String, dynamic> json) =
      _$ANOnboardingModelImpl.fromJson;

  @override
  String get id;
  @override
  String? get title;
  @override
  String? get description;
  @override
  ANImageModel get image;

  /// Create a copy of ANOnboardingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ANOnboardingModelImplCopyWith<_$ANOnboardingModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
