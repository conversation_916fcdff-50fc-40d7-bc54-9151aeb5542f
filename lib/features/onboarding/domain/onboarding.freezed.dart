// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'onboarding.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ANOnboardingModel {
  String get id;
  String? get title;
  String? get description;
  ANImageModel get image;

  /// Create a copy of ANOnboardingModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ANOnboardingModelCopyWith<ANOnboardingModel> get copyWith =>
      _$ANOnboardingModelCopyWithImpl<ANOnboardingModel>(
          this as ANOnboardingModel, _$identity);

  /// Serializes this ANOnboardingModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ANOnboardingModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.image, image) || other.image == image));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, title, description, image);

  @override
  String toString() {
    return 'ANOnboardingModel(id: $id, title: $title, description: $description, image: $image)';
  }
}

/// @nodoc
abstract mixin class $ANOnboardingModelCopyWith<$Res> {
  factory $ANOnboardingModelCopyWith(
          ANOnboardingModel value, $Res Function(ANOnboardingModel) _then) =
      _$ANOnboardingModelCopyWithImpl;
  @useResult
  $Res call(
      {String id, String? title, String? description, ANImageModel image});

  $ANImageModelCopyWith<$Res> get image;
}

/// @nodoc
class _$ANOnboardingModelCopyWithImpl<$Res>
    implements $ANOnboardingModelCopyWith<$Res> {
  _$ANOnboardingModelCopyWithImpl(this._self, this._then);

  final ANOnboardingModel _self;
  final $Res Function(ANOnboardingModel) _then;

  /// Create a copy of ANOnboardingModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = freezed,
    Object? description = freezed,
    Object? image = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      image: null == image
          ? _self.image
          : image // ignore: cast_nullable_to_non_nullable
              as ANImageModel,
    ));
  }

  /// Create a copy of ANOnboardingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ANImageModelCopyWith<$Res> get image {
    return $ANImageModelCopyWith<$Res>(_self.image, (value) {
      return _then(_self.copyWith(image: value));
    });
  }
}

/// Adds pattern-matching-related methods to [ANOnboardingModel].
extension ANOnboardingModelPatterns on ANOnboardingModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ANOnboardingModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANOnboardingModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ANOnboardingModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANOnboardingModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ANOnboardingModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANOnboardingModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String id, String? title, String? description, ANImageModel image)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANOnboardingModel() when $default != null:
        return $default(_that.id, _that.title, _that.description, _that.image);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String id, String? title, String? description, ANImageModel image)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANOnboardingModel():
        return $default(_that.id, _that.title, _that.description, _that.image);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String id, String? title, String? description, ANImageModel image)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANOnboardingModel() when $default != null:
        return $default(_that.id, _that.title, _that.description, _that.image);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ANOnboardingModel implements ANOnboardingModel {
  const _ANOnboardingModel(
      {required this.id, this.title, this.description, required this.image});
  factory _ANOnboardingModel.fromJson(Map<String, dynamic> json) =>
      _$ANOnboardingModelFromJson(json);

  @override
  final String id;
  @override
  final String? title;
  @override
  final String? description;
  @override
  final ANImageModel image;

  /// Create a copy of ANOnboardingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ANOnboardingModelCopyWith<_ANOnboardingModel> get copyWith =>
      __$ANOnboardingModelCopyWithImpl<_ANOnboardingModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ANOnboardingModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ANOnboardingModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.image, image) || other.image == image));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, title, description, image);

  @override
  String toString() {
    return 'ANOnboardingModel(id: $id, title: $title, description: $description, image: $image)';
  }
}

/// @nodoc
abstract mixin class _$ANOnboardingModelCopyWith<$Res>
    implements $ANOnboardingModelCopyWith<$Res> {
  factory _$ANOnboardingModelCopyWith(
          _ANOnboardingModel value, $Res Function(_ANOnboardingModel) _then) =
      __$ANOnboardingModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id, String? title, String? description, ANImageModel image});

  @override
  $ANImageModelCopyWith<$Res> get image;
}

/// @nodoc
class __$ANOnboardingModelCopyWithImpl<$Res>
    implements _$ANOnboardingModelCopyWith<$Res> {
  __$ANOnboardingModelCopyWithImpl(this._self, this._then);

  final _ANOnboardingModel _self;
  final $Res Function(_ANOnboardingModel) _then;

  /// Create a copy of ANOnboardingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? title = freezed,
    Object? description = freezed,
    Object? image = null,
  }) {
    return _then(_ANOnboardingModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      image: null == image
          ? _self.image
          : image // ignore: cast_nullable_to_non_nullable
              as ANImageModel,
    ));
  }

  /// Create a copy of ANOnboardingModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ANImageModelCopyWith<$Res> get image {
    return $ANImageModelCopyWith<$Res>(_self.image, (value) {
      return _then(_self.copyWith(image: value));
    });
  }
}

// dart format on
