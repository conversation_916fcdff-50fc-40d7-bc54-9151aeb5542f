import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';
import 'package:flutter/material.dart';
import 'package:qr_code_scanner_plus/qr_code_scanner_plus.dart';

// QR scanner tags
const String kFlashOn = 'FLASH ON';
const String kFlashOff = 'FLASH OFF';

const IconData kFlashOffBtnIcon = Icons.flash_off;
const IconData kFlashOnBtnIcon = Icons.flash_on;

class ANScanQrCodeScreen extends StatefulWidget {
  const ANScanQrCodeScreen({
    Key? key,
  }) : super(key: key);

  @override
  _ANScanQrCodeScreenState createState() => _ANScanQrCodeScreenState();
}

class _ANScanQrCodeScreenState extends State<ANScanQrCodeScreen> {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  Barcode? result;
  var flashState = kFlashOn;
  QRViewController? _qrViewController;
  double factor = 0.7;
  bool isScanning = false;

  bool _isFlashOn(String current) => kFlashOn == current;

  void _onQRViewCreated(QRViewController controller) {
    _qrViewController = controller;
    setState(() {
      isScanning = true;
      factor = 0.175;
    });
    controller.scannedDataStream.timeout(const Duration(seconds: 15),
        onTimeout: (value) {
      setState(() {
        isScanning = false;
        factor = 0.7;
      });
      ANDialogHelper.gShowConfirmationDialog(
        context: context,
        message: S.of(context).kNoQrCodeDetected,
        content: S.of(context).kPleaseMakeSureTheQrCodeIsVisibleAndTryAgain,
        type: DialogType.confirm,
        barrierDismissible: false,
      );
    }).listen((scanData) {
      controller.pauseCamera();
      setState(() => result = scanData);
      ANDialogHelper.gShowConfirmationDialog(
              context: context,
              message: S.of(context).kQrCodeScannedSuccessfully,
              content: S.of(context).kYouWillBeRedirectedToThePreviousScreen,
              type: DialogType.confirm,
              barrierDismissible: false)
          .whenComplete(() => Future.delayed(Duration.zero, () {
                Navigator.pop(context, scanData.code);
              }));
    });
  }

  @override
  void dispose() {
    _qrViewController!.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Expanded(
            flex: 1,
            child: Stack(
              children: [
                QRView(
                  key: qrKey,
                  onQRViewCreated: _onQRViewCreated,
                  overlay: QrScannerOverlayShape(
                    borderColor: AppColors.primaryColor,
                    borderRadius: 10,
                    borderLength: 30,
                    borderWidth: 10,
                    cutOutSize: 300,
                  ),
                ),
                Positioned(
                  left: MediaQuery.of(context).size.width * 0.1,
                  top: MediaQuery.of(context).size.height * 0.1,
                  child: IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ),
                Positioned(
                  right: MediaQuery.of(context).size.width * 0.1,
                  top: MediaQuery.of(context).size.height * 0.1,
                  child: ClipOval(
                    child: IconButton(
                      icon: Icon(
                        flashState == kFlashOff
                            ? kFlashOffBtnIcon
                            : kFlashOnBtnIcon,
                        color: Colors.white,
                      ),
                      onPressed: () {
                        if (_qrViewController != null) {
                          _qrViewController!.toggleFlash();
                          if (_isFlashOn(flashState)) {
                            setState(() => flashState = kFlashOff);
                          } else {
                            setState(() => flashState = kFlashOn);
                          }
                        }
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
