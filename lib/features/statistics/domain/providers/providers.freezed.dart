// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'providers.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ANProviderModel {
  String get id => throw _privateConstructorUsedError;
  String get user => throw _privateConstructorUsedError;
  String? get biography => throw _privateConstructorUsedError;
  List<ANExperienceModel> get experience => throw _privateConstructorUsedError;
  List<ANCertificatesModel> get certificates =>
      throw _privateConstructorUsedError;

  /// Create a copy of ANProviderModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ANProviderModelCopyWith<ANProviderModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANProviderModelCopyWith<$Res> {
  factory $ANProviderModelCopyWith(
          ANProviderModel value, $Res Function(ANProviderModel) then) =
      _$ANProviderModelCopyWithImpl<$Res, ANProviderModel>;
  @useResult
  $Res call(
      {String id,
      String user,
      String? biography,
      List<ANExperienceModel> experience,
      List<ANCertificatesModel> certificates});
}

/// @nodoc
class _$ANProviderModelCopyWithImpl<$Res, $Val extends ANProviderModel>
    implements $ANProviderModelCopyWith<$Res> {
  _$ANProviderModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ANProviderModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? user = null,
    Object? biography = freezed,
    Object? experience = null,
    Object? certificates = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as String,
      biography: freezed == biography
          ? _value.biography
          : biography // ignore: cast_nullable_to_non_nullable
              as String?,
      experience: null == experience
          ? _value.experience
          : experience // ignore: cast_nullable_to_non_nullable
              as List<ANExperienceModel>,
      certificates: null == certificates
          ? _value.certificates
          : certificates // ignore: cast_nullable_to_non_nullable
              as List<ANCertificatesModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ANProviderModelImplCopyWith<$Res>
    implements $ANProviderModelCopyWith<$Res> {
  factory _$$ANProviderModelImplCopyWith(_$ANProviderModelImpl value,
          $Res Function(_$ANProviderModelImpl) then) =
      __$$ANProviderModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String user,
      String? biography,
      List<ANExperienceModel> experience,
      List<ANCertificatesModel> certificates});
}

/// @nodoc
class __$$ANProviderModelImplCopyWithImpl<$Res>
    extends _$ANProviderModelCopyWithImpl<$Res, _$ANProviderModelImpl>
    implements _$$ANProviderModelImplCopyWith<$Res> {
  __$$ANProviderModelImplCopyWithImpl(
      _$ANProviderModelImpl _value, $Res Function(_$ANProviderModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ANProviderModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? user = null,
    Object? biography = freezed,
    Object? experience = null,
    Object? certificates = null,
  }) {
    return _then(_$ANProviderModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as String,
      biography: freezed == biography
          ? _value.biography
          : biography // ignore: cast_nullable_to_non_nullable
              as String?,
      experience: null == experience
          ? _value._experience
          : experience // ignore: cast_nullable_to_non_nullable
              as List<ANExperienceModel>,
      certificates: null == certificates
          ? _value._certificates
          : certificates // ignore: cast_nullable_to_non_nullable
              as List<ANCertificatesModel>,
    ));
  }
}

/// @nodoc

class _$ANProviderModelImpl implements _ANProviderModel {
  const _$ANProviderModelImpl(
      {required this.id,
      required this.user,
      this.biography,
      required final List<ANExperienceModel> experience,
      required final List<ANCertificatesModel> certificates})
      : _experience = experience,
        _certificates = certificates;

  @override
  final String id;
  @override
  final String user;
  @override
  final String? biography;
  final List<ANExperienceModel> _experience;
  @override
  List<ANExperienceModel> get experience {
    if (_experience is EqualUnmodifiableListView) return _experience;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_experience);
  }

  final List<ANCertificatesModel> _certificates;
  @override
  List<ANCertificatesModel> get certificates {
    if (_certificates is EqualUnmodifiableListView) return _certificates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_certificates);
  }

  @override
  String toString() {
    return 'ANProviderModel(id: $id, user: $user, biography: $biography, experience: $experience, certificates: $certificates)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ANProviderModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.biography, biography) ||
                other.biography == biography) &&
            const DeepCollectionEquality()
                .equals(other._experience, _experience) &&
            const DeepCollectionEquality()
                .equals(other._certificates, _certificates));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      user,
      biography,
      const DeepCollectionEquality().hash(_experience),
      const DeepCollectionEquality().hash(_certificates));

  /// Create a copy of ANProviderModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ANProviderModelImplCopyWith<_$ANProviderModelImpl> get copyWith =>
      __$$ANProviderModelImplCopyWithImpl<_$ANProviderModelImpl>(
          this, _$identity);
}

abstract class _ANProviderModel implements ANProviderModel {
  const factory _ANProviderModel(
          {required final String id,
          required final String user,
          final String? biography,
          required final List<ANExperienceModel> experience,
          required final List<ANCertificatesModel> certificates}) =
      _$ANProviderModelImpl;

  @override
  String get id;
  @override
  String get user;
  @override
  String? get biography;
  @override
  List<ANExperienceModel> get experience;
  @override
  List<ANCertificatesModel> get certificates;

  /// Create a copy of ANProviderModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ANProviderModelImplCopyWith<_$ANProviderModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
