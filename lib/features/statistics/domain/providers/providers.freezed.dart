// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'providers.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ANProviderModel {
  String get id;
  String get user;
  String? get biography;
  List<ANExperienceModel> get experience;
  List<ANCertificatesModel> get certificates;

  /// Create a copy of ANProviderModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ANProviderModelCopyWith<ANProviderModel> get copyWith =>
      _$ANProviderModelCopyWithImpl<ANProviderModel>(
          this as ANProviderModel, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ANProviderModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.biography, biography) ||
                other.biography == biography) &&
            const DeepCollectionEquality()
                .equals(other.experience, experience) &&
            const DeepCollectionEquality()
                .equals(other.certificates, certificates));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      user,
      biography,
      const DeepCollectionEquality().hash(experience),
      const DeepCollectionEquality().hash(certificates));

  @override
  String toString() {
    return 'ANProviderModel(id: $id, user: $user, biography: $biography, experience: $experience, certificates: $certificates)';
  }
}

/// @nodoc
abstract mixin class $ANProviderModelCopyWith<$Res> {
  factory $ANProviderModelCopyWith(
          ANProviderModel value, $Res Function(ANProviderModel) _then) =
      _$ANProviderModelCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String user,
      String? biography,
      List<ANExperienceModel> experience,
      List<ANCertificatesModel> certificates});
}

/// @nodoc
class _$ANProviderModelCopyWithImpl<$Res>
    implements $ANProviderModelCopyWith<$Res> {
  _$ANProviderModelCopyWithImpl(this._self, this._then);

  final ANProviderModel _self;
  final $Res Function(ANProviderModel) _then;

  /// Create a copy of ANProviderModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? user = null,
    Object? biography = freezed,
    Object? experience = null,
    Object? certificates = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      user: null == user
          ? _self.user
          : user // ignore: cast_nullable_to_non_nullable
              as String,
      biography: freezed == biography
          ? _self.biography
          : biography // ignore: cast_nullable_to_non_nullable
              as String?,
      experience: null == experience
          ? _self.experience
          : experience // ignore: cast_nullable_to_non_nullable
              as List<ANExperienceModel>,
      certificates: null == certificates
          ? _self.certificates
          : certificates // ignore: cast_nullable_to_non_nullable
              as List<ANCertificatesModel>,
    ));
  }
}

/// Adds pattern-matching-related methods to [ANProviderModel].
extension ANProviderModelPatterns on ANProviderModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ANProviderModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANProviderModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ANProviderModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANProviderModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ANProviderModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANProviderModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String id,
            String user,
            String? biography,
            List<ANExperienceModel> experience,
            List<ANCertificatesModel> certificates)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANProviderModel() when $default != null:
        return $default(_that.id, _that.user, _that.biography, _that.experience,
            _that.certificates);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String id,
            String user,
            String? biography,
            List<ANExperienceModel> experience,
            List<ANCertificatesModel> certificates)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANProviderModel():
        return $default(_that.id, _that.user, _that.biography, _that.experience,
            _that.certificates);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String id,
            String user,
            String? biography,
            List<ANExperienceModel> experience,
            List<ANCertificatesModel> certificates)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANProviderModel() when $default != null:
        return $default(_that.id, _that.user, _that.biography, _that.experience,
            _that.certificates);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _ANProviderModel implements ANProviderModel {
  const _ANProviderModel(
      {required this.id,
      required this.user,
      this.biography,
      required final List<ANExperienceModel> experience,
      required final List<ANCertificatesModel> certificates})
      : _experience = experience,
        _certificates = certificates;

  @override
  final String id;
  @override
  final String user;
  @override
  final String? biography;
  final List<ANExperienceModel> _experience;
  @override
  List<ANExperienceModel> get experience {
    if (_experience is EqualUnmodifiableListView) return _experience;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_experience);
  }

  final List<ANCertificatesModel> _certificates;
  @override
  List<ANCertificatesModel> get certificates {
    if (_certificates is EqualUnmodifiableListView) return _certificates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_certificates);
  }

  /// Create a copy of ANProviderModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ANProviderModelCopyWith<_ANProviderModel> get copyWith =>
      __$ANProviderModelCopyWithImpl<_ANProviderModel>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ANProviderModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.biography, biography) ||
                other.biography == biography) &&
            const DeepCollectionEquality()
                .equals(other._experience, _experience) &&
            const DeepCollectionEquality()
                .equals(other._certificates, _certificates));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      user,
      biography,
      const DeepCollectionEquality().hash(_experience),
      const DeepCollectionEquality().hash(_certificates));

  @override
  String toString() {
    return 'ANProviderModel(id: $id, user: $user, biography: $biography, experience: $experience, certificates: $certificates)';
  }
}

/// @nodoc
abstract mixin class _$ANProviderModelCopyWith<$Res>
    implements $ANProviderModelCopyWith<$Res> {
  factory _$ANProviderModelCopyWith(
          _ANProviderModel value, $Res Function(_ANProviderModel) _then) =
      __$ANProviderModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String user,
      String? biography,
      List<ANExperienceModel> experience,
      List<ANCertificatesModel> certificates});
}

/// @nodoc
class __$ANProviderModelCopyWithImpl<$Res>
    implements _$ANProviderModelCopyWith<$Res> {
  __$ANProviderModelCopyWithImpl(this._self, this._then);

  final _ANProviderModel _self;
  final $Res Function(_ANProviderModel) _then;

  /// Create a copy of ANProviderModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? user = null,
    Object? biography = freezed,
    Object? experience = null,
    Object? certificates = null,
  }) {
    return _then(_ANProviderModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      user: null == user
          ? _self.user
          : user // ignore: cast_nullable_to_non_nullable
              as String,
      biography: freezed == biography
          ? _self.biography
          : biography // ignore: cast_nullable_to_non_nullable
              as String?,
      experience: null == experience
          ? _self._experience
          : experience // ignore: cast_nullable_to_non_nullable
              as List<ANExperienceModel>,
      certificates: null == certificates
          ? _self._certificates
          : certificates // ignore: cast_nullable_to_non_nullable
              as List<ANCertificatesModel>,
    ));
  }
}

// dart format on
