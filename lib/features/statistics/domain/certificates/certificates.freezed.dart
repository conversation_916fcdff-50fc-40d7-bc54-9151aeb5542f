// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'certificates.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ANCertificatesModel {
  String get id;
  String? get certificateName;
  String? get issuingInstitution;
  DateTime? get issueDate;

  /// Create a copy of ANCertificatesModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ANCertificatesModelCopyWith<ANCertificatesModel> get copyWith =>
      _$ANCertificatesModelCopyWithImpl<ANCertificatesModel>(
          this as ANCertificatesModel, _$identity);

  /// Serializes this ANCertificatesModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ANCertificatesModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.certificateName, certificateName) ||
                other.certificateName == certificateName) &&
            (identical(other.issuingInstitution, issuingInstitution) ||
                other.issuingInstitution == issuingInstitution) &&
            (identical(other.issueDate, issueDate) ||
                other.issueDate == issueDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, certificateName, issuingInstitution, issueDate);

  @override
  String toString() {
    return 'ANCertificatesModel(id: $id, certificateName: $certificateName, issuingInstitution: $issuingInstitution, issueDate: $issueDate)';
  }
}

/// @nodoc
abstract mixin class $ANCertificatesModelCopyWith<$Res> {
  factory $ANCertificatesModelCopyWith(
          ANCertificatesModel value, $Res Function(ANCertificatesModel) _then) =
      _$ANCertificatesModelCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String? certificateName,
      String? issuingInstitution,
      DateTime? issueDate});
}

/// @nodoc
class _$ANCertificatesModelCopyWithImpl<$Res>
    implements $ANCertificatesModelCopyWith<$Res> {
  _$ANCertificatesModelCopyWithImpl(this._self, this._then);

  final ANCertificatesModel _self;
  final $Res Function(ANCertificatesModel) _then;

  /// Create a copy of ANCertificatesModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? certificateName = freezed,
    Object? issuingInstitution = freezed,
    Object? issueDate = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      certificateName: freezed == certificateName
          ? _self.certificateName
          : certificateName // ignore: cast_nullable_to_non_nullable
              as String?,
      issuingInstitution: freezed == issuingInstitution
          ? _self.issuingInstitution
          : issuingInstitution // ignore: cast_nullable_to_non_nullable
              as String?,
      issueDate: freezed == issueDate
          ? _self.issueDate
          : issueDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// Adds pattern-matching-related methods to [ANCertificatesModel].
extension ANCertificatesModelPatterns on ANCertificatesModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ANCertificatesModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANCertificatesModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ANCertificatesModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANCertificatesModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ANCertificatesModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANCertificatesModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String id, String? certificateName,
            String? issuingInstitution, DateTime? issueDate)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANCertificatesModel() when $default != null:
        return $default(_that.id, _that.certificateName,
            _that.issuingInstitution, _that.issueDate);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String id, String? certificateName,
            String? issuingInstitution, DateTime? issueDate)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANCertificatesModel():
        return $default(_that.id, _that.certificateName,
            _that.issuingInstitution, _that.issueDate);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String id, String? certificateName,
            String? issuingInstitution, DateTime? issueDate)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANCertificatesModel() when $default != null:
        return $default(_that.id, _that.certificateName,
            _that.issuingInstitution, _that.issueDate);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ANCertificatesModel implements ANCertificatesModel {
  const _ANCertificatesModel(
      {required this.id,
      this.certificateName,
      this.issuingInstitution,
      this.issueDate});
  factory _ANCertificatesModel.fromJson(Map<String, dynamic> json) =>
      _$ANCertificatesModelFromJson(json);

  @override
  final String id;
  @override
  final String? certificateName;
  @override
  final String? issuingInstitution;
  @override
  final DateTime? issueDate;

  /// Create a copy of ANCertificatesModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ANCertificatesModelCopyWith<_ANCertificatesModel> get copyWith =>
      __$ANCertificatesModelCopyWithImpl<_ANCertificatesModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ANCertificatesModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ANCertificatesModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.certificateName, certificateName) ||
                other.certificateName == certificateName) &&
            (identical(other.issuingInstitution, issuingInstitution) ||
                other.issuingInstitution == issuingInstitution) &&
            (identical(other.issueDate, issueDate) ||
                other.issueDate == issueDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, certificateName, issuingInstitution, issueDate);

  @override
  String toString() {
    return 'ANCertificatesModel(id: $id, certificateName: $certificateName, issuingInstitution: $issuingInstitution, issueDate: $issueDate)';
  }
}

/// @nodoc
abstract mixin class _$ANCertificatesModelCopyWith<$Res>
    implements $ANCertificatesModelCopyWith<$Res> {
  factory _$ANCertificatesModelCopyWith(_ANCertificatesModel value,
          $Res Function(_ANCertificatesModel) _then) =
      __$ANCertificatesModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String? certificateName,
      String? issuingInstitution,
      DateTime? issueDate});
}

/// @nodoc
class __$ANCertificatesModelCopyWithImpl<$Res>
    implements _$ANCertificatesModelCopyWith<$Res> {
  __$ANCertificatesModelCopyWithImpl(this._self, this._then);

  final _ANCertificatesModel _self;
  final $Res Function(_ANCertificatesModel) _then;

  /// Create a copy of ANCertificatesModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? certificateName = freezed,
    Object? issuingInstitution = freezed,
    Object? issueDate = freezed,
  }) {
    return _then(_ANCertificatesModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      certificateName: freezed == certificateName
          ? _self.certificateName
          : certificateName // ignore: cast_nullable_to_non_nullable
              as String?,
      issuingInstitution: freezed == issuingInstitution
          ? _self.issuingInstitution
          : issuingInstitution // ignore: cast_nullable_to_non_nullable
              as String?,
      issueDate: freezed == issueDate
          ? _self.issueDate
          : issueDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

// dart format on
