// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'service_gallery.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ANServiceGalleryModel _$ANServiceGalleryModelFromJson(
    Map<String, dynamic> json) {
  return _ANServiceGalleryModel.fromJson(json);
}

/// @nodoc
mixin _$ANServiceGalleryModel {
  String get id => throw _privateConstructorUsedError;
  ANImageModel get logo => throw _privateConstructorUsedError;
  @JsonKey(name: "pin_location")
  List<double> get pinLocation => throw _privateConstructorUsedError;

  /// Serializes this ANServiceGalleryModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ANServiceGalleryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ANServiceGalleryModelCopyWith<ANServiceGalleryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANServiceGalleryModelCopyWith<$Res> {
  factory $ANServiceGalleryModelCopyWith(ANServiceGalleryModel value,
          $Res Function(ANServiceGalleryModel) then) =
      _$ANServiceGalleryModelCopyWithImpl<$Res, ANServiceGalleryModel>;
  @useResult
  $Res call(
      {String id,
      ANImageModel logo,
      @JsonKey(name: "pin_location") List<double> pinLocation});

  $ANImageModelCopyWith<$Res> get logo;
}

/// @nodoc
class _$ANServiceGalleryModelCopyWithImpl<$Res,
        $Val extends ANServiceGalleryModel>
    implements $ANServiceGalleryModelCopyWith<$Res> {
  _$ANServiceGalleryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ANServiceGalleryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? logo = null,
    Object? pinLocation = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      logo: null == logo
          ? _value.logo
          : logo // ignore: cast_nullable_to_non_nullable
              as ANImageModel,
      pinLocation: null == pinLocation
          ? _value.pinLocation
          : pinLocation // ignore: cast_nullable_to_non_nullable
              as List<double>,
    ) as $Val);
  }

  /// Create a copy of ANServiceGalleryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ANImageModelCopyWith<$Res> get logo {
    return $ANImageModelCopyWith<$Res>(_value.logo, (value) {
      return _then(_value.copyWith(logo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ANServiceGalleryModelImplCopyWith<$Res>
    implements $ANServiceGalleryModelCopyWith<$Res> {
  factory _$$ANServiceGalleryModelImplCopyWith(
          _$ANServiceGalleryModelImpl value,
          $Res Function(_$ANServiceGalleryModelImpl) then) =
      __$$ANServiceGalleryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      ANImageModel logo,
      @JsonKey(name: "pin_location") List<double> pinLocation});

  @override
  $ANImageModelCopyWith<$Res> get logo;
}

/// @nodoc
class __$$ANServiceGalleryModelImplCopyWithImpl<$Res>
    extends _$ANServiceGalleryModelCopyWithImpl<$Res,
        _$ANServiceGalleryModelImpl>
    implements _$$ANServiceGalleryModelImplCopyWith<$Res> {
  __$$ANServiceGalleryModelImplCopyWithImpl(_$ANServiceGalleryModelImpl _value,
      $Res Function(_$ANServiceGalleryModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ANServiceGalleryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? logo = null,
    Object? pinLocation = null,
  }) {
    return _then(_$ANServiceGalleryModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      logo: null == logo
          ? _value.logo
          : logo // ignore: cast_nullable_to_non_nullable
              as ANImageModel,
      pinLocation: null == pinLocation
          ? _value._pinLocation
          : pinLocation // ignore: cast_nullable_to_non_nullable
              as List<double>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ANServiceGalleryModelImpl implements _ANServiceGalleryModel {
  const _$ANServiceGalleryModelImpl(
      {required this.id,
      required this.logo,
      @JsonKey(name: "pin_location")
      final List<double> pinLocation = const [31.221269, 30.054482]})
      : _pinLocation = pinLocation;

  factory _$ANServiceGalleryModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ANServiceGalleryModelImplFromJson(json);

  @override
  final String id;
  @override
  final ANImageModel logo;
  final List<double> _pinLocation;
  @override
  @JsonKey(name: "pin_location")
  List<double> get pinLocation {
    if (_pinLocation is EqualUnmodifiableListView) return _pinLocation;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_pinLocation);
  }

  @override
  String toString() {
    return 'ANServiceGalleryModel(id: $id, logo: $logo, pinLocation: $pinLocation)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ANServiceGalleryModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.logo, logo) || other.logo == logo) &&
            const DeepCollectionEquality()
                .equals(other._pinLocation, _pinLocation));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, logo, const DeepCollectionEquality().hash(_pinLocation));

  /// Create a copy of ANServiceGalleryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ANServiceGalleryModelImplCopyWith<_$ANServiceGalleryModelImpl>
      get copyWith => __$$ANServiceGalleryModelImplCopyWithImpl<
          _$ANServiceGalleryModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ANServiceGalleryModelImplToJson(
      this,
    );
  }
}

abstract class _ANServiceGalleryModel implements ANServiceGalleryModel {
  const factory _ANServiceGalleryModel(
          {required final String id,
          required final ANImageModel logo,
          @JsonKey(name: "pin_location") final List<double> pinLocation}) =
      _$ANServiceGalleryModelImpl;

  factory _ANServiceGalleryModel.fromJson(Map<String, dynamic> json) =
      _$ANServiceGalleryModelImpl.fromJson;

  @override
  String get id;
  @override
  ANImageModel get logo;
  @override
  @JsonKey(name: "pin_location")
  List<double> get pinLocation;

  /// Create a copy of ANServiceGalleryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ANServiceGalleryModelImplCopyWith<_$ANServiceGalleryModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
