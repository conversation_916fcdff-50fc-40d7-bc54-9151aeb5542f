// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'service_gallery.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ANServiceGalleryModel {
  String get id;
  ANImageModel get logo;
  @JsonKey(name: "pin_location")
  List<double> get pinLocation;

  /// Create a copy of ANServiceGalleryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ANServiceGalleryModelCopyWith<ANServiceGalleryModel> get copyWith =>
      _$ANServiceGalleryModelCopyWithImpl<ANServiceGalleryModel>(
          this as ANServiceGalleryModel, _$identity);

  /// Serializes this ANServiceGalleryModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ANServiceGalleryModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.logo, logo) || other.logo == logo) &&
            const DeepCollectionEquality()
                .equals(other.pinLocation, pinLocation));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, logo, const DeepCollectionEquality().hash(pinLocation));

  @override
  String toString() {
    return 'ANServiceGalleryModel(id: $id, logo: $logo, pinLocation: $pinLocation)';
  }
}

/// @nodoc
abstract mixin class $ANServiceGalleryModelCopyWith<$Res> {
  factory $ANServiceGalleryModelCopyWith(ANServiceGalleryModel value,
          $Res Function(ANServiceGalleryModel) _then) =
      _$ANServiceGalleryModelCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      ANImageModel logo,
      @JsonKey(name: "pin_location") List<double> pinLocation});

  $ANImageModelCopyWith<$Res> get logo;
}

/// @nodoc
class _$ANServiceGalleryModelCopyWithImpl<$Res>
    implements $ANServiceGalleryModelCopyWith<$Res> {
  _$ANServiceGalleryModelCopyWithImpl(this._self, this._then);

  final ANServiceGalleryModel _self;
  final $Res Function(ANServiceGalleryModel) _then;

  /// Create a copy of ANServiceGalleryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? logo = null,
    Object? pinLocation = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      logo: null == logo
          ? _self.logo
          : logo // ignore: cast_nullable_to_non_nullable
              as ANImageModel,
      pinLocation: null == pinLocation
          ? _self.pinLocation
          : pinLocation // ignore: cast_nullable_to_non_nullable
              as List<double>,
    ));
  }

  /// Create a copy of ANServiceGalleryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ANImageModelCopyWith<$Res> get logo {
    return $ANImageModelCopyWith<$Res>(_self.logo, (value) {
      return _then(_self.copyWith(logo: value));
    });
  }
}

/// Adds pattern-matching-related methods to [ANServiceGalleryModel].
extension ANServiceGalleryModelPatterns on ANServiceGalleryModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ANServiceGalleryModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANServiceGalleryModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ANServiceGalleryModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANServiceGalleryModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ANServiceGalleryModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANServiceGalleryModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String id, ANImageModel logo,
            @JsonKey(name: "pin_location") List<double> pinLocation)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANServiceGalleryModel() when $default != null:
        return $default(_that.id, _that.logo, _that.pinLocation);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String id, ANImageModel logo,
            @JsonKey(name: "pin_location") List<double> pinLocation)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANServiceGalleryModel():
        return $default(_that.id, _that.logo, _that.pinLocation);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String id, ANImageModel logo,
            @JsonKey(name: "pin_location") List<double> pinLocation)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANServiceGalleryModel() when $default != null:
        return $default(_that.id, _that.logo, _that.pinLocation);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ANServiceGalleryModel implements ANServiceGalleryModel {
  const _ANServiceGalleryModel(
      {required this.id,
      required this.logo,
      @JsonKey(name: "pin_location")
      final List<double> pinLocation = const [31.221269, 30.054482]})
      : _pinLocation = pinLocation;
  factory _ANServiceGalleryModel.fromJson(Map<String, dynamic> json) =>
      _$ANServiceGalleryModelFromJson(json);

  @override
  final String id;
  @override
  final ANImageModel logo;
  final List<double> _pinLocation;
  @override
  @JsonKey(name: "pin_location")
  List<double> get pinLocation {
    if (_pinLocation is EqualUnmodifiableListView) return _pinLocation;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_pinLocation);
  }

  /// Create a copy of ANServiceGalleryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ANServiceGalleryModelCopyWith<_ANServiceGalleryModel> get copyWith =>
      __$ANServiceGalleryModelCopyWithImpl<_ANServiceGalleryModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ANServiceGalleryModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ANServiceGalleryModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.logo, logo) || other.logo == logo) &&
            const DeepCollectionEquality()
                .equals(other._pinLocation, _pinLocation));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, logo, const DeepCollectionEquality().hash(_pinLocation));

  @override
  String toString() {
    return 'ANServiceGalleryModel(id: $id, logo: $logo, pinLocation: $pinLocation)';
  }
}

/// @nodoc
abstract mixin class _$ANServiceGalleryModelCopyWith<$Res>
    implements $ANServiceGalleryModelCopyWith<$Res> {
  factory _$ANServiceGalleryModelCopyWith(_ANServiceGalleryModel value,
          $Res Function(_ANServiceGalleryModel) _then) =
      __$ANServiceGalleryModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      ANImageModel logo,
      @JsonKey(name: "pin_location") List<double> pinLocation});

  @override
  $ANImageModelCopyWith<$Res> get logo;
}

/// @nodoc
class __$ANServiceGalleryModelCopyWithImpl<$Res>
    implements _$ANServiceGalleryModelCopyWith<$Res> {
  __$ANServiceGalleryModelCopyWithImpl(this._self, this._then);

  final _ANServiceGalleryModel _self;
  final $Res Function(_ANServiceGalleryModel) _then;

  /// Create a copy of ANServiceGalleryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? logo = null,
    Object? pinLocation = null,
  }) {
    return _then(_ANServiceGalleryModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      logo: null == logo
          ? _self.logo
          : logo // ignore: cast_nullable_to_non_nullable
              as ANImageModel,
      pinLocation: null == pinLocation
          ? _self._pinLocation
          : pinLocation // ignore: cast_nullable_to_non_nullable
              as List<double>,
    ));
  }

  /// Create a copy of ANServiceGalleryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ANImageModelCopyWith<$Res> get logo {
    return $ANImageModelCopyWith<$Res>(_self.logo, (value) {
      return _then(_self.copyWith(logo: value));
    });
  }
}

// dart format on
