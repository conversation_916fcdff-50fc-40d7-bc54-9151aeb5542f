// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'services.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ANServicesModel _$ANServicesModelFromJson(Map<String, dynamic> json) =>
    _ANServicesModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      minPrice: (json['minPrice'] as num?)?.toDouble(),
      maxPrice: (json['maxPrice'] as num?)?.toDouble(),
      discount: (json['discount'] as num?)?.toDouble(),
      discountActive: json['discountActive'] as bool?,
      image: json['image'] == null
          ? null
          : ANImageModel.fromJson(json['image'] as Map<String, dynamic>),
      isPackage: json['isPackage'] as bool,
      schedule: (json['schedule'] as List<dynamic>?)
              ?.map((e) => ANScheduleModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$ANServicesModelToJson(_ANServicesModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'price': instance.price,
      'minPrice': instance.minPrice,
      'maxPrice': instance.maxPrice,
      'discount': instance.discount,
      'discountActive': instance.discountActive,
      'image': instance.image,
      'isPackage': instance.isPackage,
      'schedule': instance.schedule,
    };
