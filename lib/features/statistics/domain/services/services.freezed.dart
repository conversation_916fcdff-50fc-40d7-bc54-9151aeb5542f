// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'services.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ANServicesModel {
  String get id;
  String get title;
  String get description;
  double get price;
  double? get minPrice;
  double? get maxPrice;
  double? get discount;
  bool? get discountActive;
  ANImageModel? get image;
  bool get isPackage;
  List<ANScheduleModel> get schedule;

  /// Create a copy of ANServicesModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ANServicesModelCopyWith<ANServicesModel> get copyWith =>
      _$ANServicesModelCopyWithImpl<ANServicesModel>(
          this as ANServicesModel, _$identity);

  /// Serializes this ANServicesModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ANServicesModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.minPrice, minPrice) ||
                other.minPrice == minPrice) &&
            (identical(other.maxPrice, maxPrice) ||
                other.maxPrice == maxPrice) &&
            (identical(other.discount, discount) ||
                other.discount == discount) &&
            (identical(other.discountActive, discountActive) ||
                other.discountActive == discountActive) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.isPackage, isPackage) ||
                other.isPackage == isPackage) &&
            const DeepCollectionEquality().equals(other.schedule, schedule));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      title,
      description,
      price,
      minPrice,
      maxPrice,
      discount,
      discountActive,
      image,
      isPackage,
      const DeepCollectionEquality().hash(schedule));

  @override
  String toString() {
    return 'ANServicesModel(id: $id, title: $title, description: $description, price: $price, minPrice: $minPrice, maxPrice: $maxPrice, discount: $discount, discountActive: $discountActive, image: $image, isPackage: $isPackage, schedule: $schedule)';
  }
}

/// @nodoc
abstract mixin class $ANServicesModelCopyWith<$Res> {
  factory $ANServicesModelCopyWith(
          ANServicesModel value, $Res Function(ANServicesModel) _then) =
      _$ANServicesModelCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String title,
      String description,
      double price,
      double? minPrice,
      double? maxPrice,
      double? discount,
      bool? discountActive,
      ANImageModel? image,
      bool isPackage,
      List<ANScheduleModel> schedule});

  $ANImageModelCopyWith<$Res>? get image;
}

/// @nodoc
class _$ANServicesModelCopyWithImpl<$Res>
    implements $ANServicesModelCopyWith<$Res> {
  _$ANServicesModelCopyWithImpl(this._self, this._then);

  final ANServicesModel _self;
  final $Res Function(ANServicesModel) _then;

  /// Create a copy of ANServicesModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? price = null,
    Object? minPrice = freezed,
    Object? maxPrice = freezed,
    Object? discount = freezed,
    Object? discountActive = freezed,
    Object? image = freezed,
    Object? isPackage = null,
    Object? schedule = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _self.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      minPrice: freezed == minPrice
          ? _self.minPrice
          : minPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      maxPrice: freezed == maxPrice
          ? _self.maxPrice
          : maxPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      discount: freezed == discount
          ? _self.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as double?,
      discountActive: freezed == discountActive
          ? _self.discountActive
          : discountActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      image: freezed == image
          ? _self.image
          : image // ignore: cast_nullable_to_non_nullable
              as ANImageModel?,
      isPackage: null == isPackage
          ? _self.isPackage
          : isPackage // ignore: cast_nullable_to_non_nullable
              as bool,
      schedule: null == schedule
          ? _self.schedule
          : schedule // ignore: cast_nullable_to_non_nullable
              as List<ANScheduleModel>,
    ));
  }

  /// Create a copy of ANServicesModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ANImageModelCopyWith<$Res>? get image {
    if (_self.image == null) {
      return null;
    }

    return $ANImageModelCopyWith<$Res>(_self.image!, (value) {
      return _then(_self.copyWith(image: value));
    });
  }
}

/// Adds pattern-matching-related methods to [ANServicesModel].
extension ANServicesModelPatterns on ANServicesModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ANServicesModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANServicesModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ANServicesModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANServicesModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ANServicesModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANServicesModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String id,
            String title,
            String description,
            double price,
            double? minPrice,
            double? maxPrice,
            double? discount,
            bool? discountActive,
            ANImageModel? image,
            bool isPackage,
            List<ANScheduleModel> schedule)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANServicesModel() when $default != null:
        return $default(
            _that.id,
            _that.title,
            _that.description,
            _that.price,
            _that.minPrice,
            _that.maxPrice,
            _that.discount,
            _that.discountActive,
            _that.image,
            _that.isPackage,
            _that.schedule);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String id,
            String title,
            String description,
            double price,
            double? minPrice,
            double? maxPrice,
            double? discount,
            bool? discountActive,
            ANImageModel? image,
            bool isPackage,
            List<ANScheduleModel> schedule)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANServicesModel():
        return $default(
            _that.id,
            _that.title,
            _that.description,
            _that.price,
            _that.minPrice,
            _that.maxPrice,
            _that.discount,
            _that.discountActive,
            _that.image,
            _that.isPackage,
            _that.schedule);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String id,
            String title,
            String description,
            double price,
            double? minPrice,
            double? maxPrice,
            double? discount,
            bool? discountActive,
            ANImageModel? image,
            bool isPackage,
            List<ANScheduleModel> schedule)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANServicesModel() when $default != null:
        return $default(
            _that.id,
            _that.title,
            _that.description,
            _that.price,
            _that.minPrice,
            _that.maxPrice,
            _that.discount,
            _that.discountActive,
            _that.image,
            _that.isPackage,
            _that.schedule);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ANServicesModel implements ANServicesModel {
  const _ANServicesModel(
      {required this.id,
      required this.title,
      required this.description,
      required this.price,
      this.minPrice,
      this.maxPrice,
      this.discount,
      this.discountActive,
      this.image,
      required this.isPackage,
      final List<ANScheduleModel> schedule = const []})
      : _schedule = schedule;
  factory _ANServicesModel.fromJson(Map<String, dynamic> json) =>
      _$ANServicesModelFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  @override
  final double price;
  @override
  final double? minPrice;
  @override
  final double? maxPrice;
  @override
  final double? discount;
  @override
  final bool? discountActive;
  @override
  final ANImageModel? image;
  @override
  final bool isPackage;
  final List<ANScheduleModel> _schedule;
  @override
  @JsonKey()
  List<ANScheduleModel> get schedule {
    if (_schedule is EqualUnmodifiableListView) return _schedule;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_schedule);
  }

  /// Create a copy of ANServicesModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ANServicesModelCopyWith<_ANServicesModel> get copyWith =>
      __$ANServicesModelCopyWithImpl<_ANServicesModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ANServicesModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ANServicesModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.minPrice, minPrice) ||
                other.minPrice == minPrice) &&
            (identical(other.maxPrice, maxPrice) ||
                other.maxPrice == maxPrice) &&
            (identical(other.discount, discount) ||
                other.discount == discount) &&
            (identical(other.discountActive, discountActive) ||
                other.discountActive == discountActive) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.isPackage, isPackage) ||
                other.isPackage == isPackage) &&
            const DeepCollectionEquality().equals(other._schedule, _schedule));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      title,
      description,
      price,
      minPrice,
      maxPrice,
      discount,
      discountActive,
      image,
      isPackage,
      const DeepCollectionEquality().hash(_schedule));

  @override
  String toString() {
    return 'ANServicesModel(id: $id, title: $title, description: $description, price: $price, minPrice: $minPrice, maxPrice: $maxPrice, discount: $discount, discountActive: $discountActive, image: $image, isPackage: $isPackage, schedule: $schedule)';
  }
}

/// @nodoc
abstract mixin class _$ANServicesModelCopyWith<$Res>
    implements $ANServicesModelCopyWith<$Res> {
  factory _$ANServicesModelCopyWith(
          _ANServicesModel value, $Res Function(_ANServicesModel) _then) =
      __$ANServicesModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String title,
      String description,
      double price,
      double? minPrice,
      double? maxPrice,
      double? discount,
      bool? discountActive,
      ANImageModel? image,
      bool isPackage,
      List<ANScheduleModel> schedule});

  @override
  $ANImageModelCopyWith<$Res>? get image;
}

/// @nodoc
class __$ANServicesModelCopyWithImpl<$Res>
    implements _$ANServicesModelCopyWith<$Res> {
  __$ANServicesModelCopyWithImpl(this._self, this._then);

  final _ANServicesModel _self;
  final $Res Function(_ANServicesModel) _then;

  /// Create a copy of ANServicesModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? price = null,
    Object? minPrice = freezed,
    Object? maxPrice = freezed,
    Object? discount = freezed,
    Object? discountActive = freezed,
    Object? image = freezed,
    Object? isPackage = null,
    Object? schedule = null,
  }) {
    return _then(_ANServicesModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _self.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      minPrice: freezed == minPrice
          ? _self.minPrice
          : minPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      maxPrice: freezed == maxPrice
          ? _self.maxPrice
          : maxPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      discount: freezed == discount
          ? _self.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as double?,
      discountActive: freezed == discountActive
          ? _self.discountActive
          : discountActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      image: freezed == image
          ? _self.image
          : image // ignore: cast_nullable_to_non_nullable
              as ANImageModel?,
      isPackage: null == isPackage
          ? _self.isPackage
          : isPackage // ignore: cast_nullable_to_non_nullable
              as bool,
      schedule: null == schedule
          ? _self._schedule
          : schedule // ignore: cast_nullable_to_non_nullable
              as List<ANScheduleModel>,
    ));
  }

  /// Create a copy of ANServicesModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ANImageModelCopyWith<$Res>? get image {
    if (_self.image == null) {
      return null;
    }

    return $ANImageModelCopyWith<$Res>(_self.image!, (value) {
      return _then(_self.copyWith(image: value));
    });
  }
}

// dart format on
