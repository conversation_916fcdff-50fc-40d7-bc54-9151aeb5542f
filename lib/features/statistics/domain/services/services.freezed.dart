// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'services.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ANServicesModel _$ANServicesModelFromJson(Map<String, dynamic> json) {
  return _ANServicesModel.fromJson(json);
}

/// @nodoc
mixin _$ANServicesModel {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  double? get minPrice => throw _privateConstructorUsedError;
  double? get maxPrice => throw _privateConstructorUsedError;
  double? get discount => throw _privateConstructorUsedError;
  bool? get discountActive => throw _privateConstructorUsedError;
  ANImageModel? get image => throw _privateConstructorUsedError;
  bool get isPackage => throw _privateConstructorUsedError;
  List<ANScheduleModel> get schedule => throw _privateConstructorUsedError;

  /// Serializes this ANServicesModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ANServicesModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ANServicesModelCopyWith<ANServicesModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANServicesModelCopyWith<$Res> {
  factory $ANServicesModelCopyWith(
          ANServicesModel value, $Res Function(ANServicesModel) then) =
      _$ANServicesModelCopyWithImpl<$Res, ANServicesModel>;
  @useResult
  $Res call(
      {String id,
      String title,
      String description,
      double price,
      double? minPrice,
      double? maxPrice,
      double? discount,
      bool? discountActive,
      ANImageModel? image,
      bool isPackage,
      List<ANScheduleModel> schedule});

  $ANImageModelCopyWith<$Res>? get image;
}

/// @nodoc
class _$ANServicesModelCopyWithImpl<$Res, $Val extends ANServicesModel>
    implements $ANServicesModelCopyWith<$Res> {
  _$ANServicesModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ANServicesModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? price = null,
    Object? minPrice = freezed,
    Object? maxPrice = freezed,
    Object? discount = freezed,
    Object? discountActive = freezed,
    Object? image = freezed,
    Object? isPackage = null,
    Object? schedule = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      minPrice: freezed == minPrice
          ? _value.minPrice
          : minPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      maxPrice: freezed == maxPrice
          ? _value.maxPrice
          : maxPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as double?,
      discountActive: freezed == discountActive
          ? _value.discountActive
          : discountActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as ANImageModel?,
      isPackage: null == isPackage
          ? _value.isPackage
          : isPackage // ignore: cast_nullable_to_non_nullable
              as bool,
      schedule: null == schedule
          ? _value.schedule
          : schedule // ignore: cast_nullable_to_non_nullable
              as List<ANScheduleModel>,
    ) as $Val);
  }

  /// Create a copy of ANServicesModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ANImageModelCopyWith<$Res>? get image {
    if (_value.image == null) {
      return null;
    }

    return $ANImageModelCopyWith<$Res>(_value.image!, (value) {
      return _then(_value.copyWith(image: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ANServicesModelImplCopyWith<$Res>
    implements $ANServicesModelCopyWith<$Res> {
  factory _$$ANServicesModelImplCopyWith(_$ANServicesModelImpl value,
          $Res Function(_$ANServicesModelImpl) then) =
      __$$ANServicesModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String title,
      String description,
      double price,
      double? minPrice,
      double? maxPrice,
      double? discount,
      bool? discountActive,
      ANImageModel? image,
      bool isPackage,
      List<ANScheduleModel> schedule});

  @override
  $ANImageModelCopyWith<$Res>? get image;
}

/// @nodoc
class __$$ANServicesModelImplCopyWithImpl<$Res>
    extends _$ANServicesModelCopyWithImpl<$Res, _$ANServicesModelImpl>
    implements _$$ANServicesModelImplCopyWith<$Res> {
  __$$ANServicesModelImplCopyWithImpl(
      _$ANServicesModelImpl _value, $Res Function(_$ANServicesModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ANServicesModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? price = null,
    Object? minPrice = freezed,
    Object? maxPrice = freezed,
    Object? discount = freezed,
    Object? discountActive = freezed,
    Object? image = freezed,
    Object? isPackage = null,
    Object? schedule = null,
  }) {
    return _then(_$ANServicesModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      minPrice: freezed == minPrice
          ? _value.minPrice
          : minPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      maxPrice: freezed == maxPrice
          ? _value.maxPrice
          : maxPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as double?,
      discountActive: freezed == discountActive
          ? _value.discountActive
          : discountActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as ANImageModel?,
      isPackage: null == isPackage
          ? _value.isPackage
          : isPackage // ignore: cast_nullable_to_non_nullable
              as bool,
      schedule: null == schedule
          ? _value._schedule
          : schedule // ignore: cast_nullable_to_non_nullable
              as List<ANScheduleModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ANServicesModelImpl implements _ANServicesModel {
  const _$ANServicesModelImpl(
      {required this.id,
      required this.title,
      required this.description,
      required this.price,
      this.minPrice,
      this.maxPrice,
      this.discount,
      this.discountActive,
      this.image,
      required this.isPackage,
      final List<ANScheduleModel> schedule = const []})
      : _schedule = schedule;

  factory _$ANServicesModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ANServicesModelImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  @override
  final double price;
  @override
  final double? minPrice;
  @override
  final double? maxPrice;
  @override
  final double? discount;
  @override
  final bool? discountActive;
  @override
  final ANImageModel? image;
  @override
  final bool isPackage;
  final List<ANScheduleModel> _schedule;
  @override
  @JsonKey()
  List<ANScheduleModel> get schedule {
    if (_schedule is EqualUnmodifiableListView) return _schedule;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_schedule);
  }

  @override
  String toString() {
    return 'ANServicesModel(id: $id, title: $title, description: $description, price: $price, minPrice: $minPrice, maxPrice: $maxPrice, discount: $discount, discountActive: $discountActive, image: $image, isPackage: $isPackage, schedule: $schedule)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ANServicesModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.minPrice, minPrice) ||
                other.minPrice == minPrice) &&
            (identical(other.maxPrice, maxPrice) ||
                other.maxPrice == maxPrice) &&
            (identical(other.discount, discount) ||
                other.discount == discount) &&
            (identical(other.discountActive, discountActive) ||
                other.discountActive == discountActive) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.isPackage, isPackage) ||
                other.isPackage == isPackage) &&
            const DeepCollectionEquality().equals(other._schedule, _schedule));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      title,
      description,
      price,
      minPrice,
      maxPrice,
      discount,
      discountActive,
      image,
      isPackage,
      const DeepCollectionEquality().hash(_schedule));

  /// Create a copy of ANServicesModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ANServicesModelImplCopyWith<_$ANServicesModelImpl> get copyWith =>
      __$$ANServicesModelImplCopyWithImpl<_$ANServicesModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ANServicesModelImplToJson(
      this,
    );
  }
}

abstract class _ANServicesModel implements ANServicesModel {
  const factory _ANServicesModel(
      {required final String id,
      required final String title,
      required final String description,
      required final double price,
      final double? minPrice,
      final double? maxPrice,
      final double? discount,
      final bool? discountActive,
      final ANImageModel? image,
      required final bool isPackage,
      final List<ANScheduleModel> schedule}) = _$ANServicesModelImpl;

  factory _ANServicesModel.fromJson(Map<String, dynamic> json) =
      _$ANServicesModelImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String get description;
  @override
  double get price;
  @override
  double? get minPrice;
  @override
  double? get maxPrice;
  @override
  double? get discount;
  @override
  bool? get discountActive;
  @override
  ANImageModel? get image;
  @override
  bool get isPackage;
  @override
  List<ANScheduleModel> get schedule;

  /// Create a copy of ANServicesModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ANServicesModelImplCopyWith<_$ANServicesModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
