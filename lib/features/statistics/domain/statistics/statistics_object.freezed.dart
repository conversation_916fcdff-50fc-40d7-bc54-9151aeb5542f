// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'statistics_object.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ANStatisticsModel _$ANStatisticsModelFromJson(Map<String, dynamic> json) {
  return _ANStatisticsModel.fromJson(json);
}

/// @nodoc
mixin _$ANStatisticsModel {
  List<dynamic> get completed => throw _privateConstructorUsedError;
  List<dynamic> get cancelled => throw _privateConstructorUsedError;

  /// Serializes this ANStatisticsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ANStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ANStatisticsModelCopyWith<ANStatisticsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANStatisticsModelCopyWith<$Res> {
  factory $ANStatisticsModelCopyWith(
          ANStatisticsModel value, $Res Function(ANStatisticsModel) then) =
      _$ANStatisticsModelCopyWithImpl<$Res, ANStatisticsModel>;
  @useResult
  $Res call({List<dynamic> completed, List<dynamic> cancelled});
}

/// @nodoc
class _$ANStatisticsModelCopyWithImpl<$Res, $Val extends ANStatisticsModel>
    implements $ANStatisticsModelCopyWith<$Res> {
  _$ANStatisticsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ANStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? completed = null,
    Object? cancelled = null,
  }) {
    return _then(_value.copyWith(
      completed: null == completed
          ? _value.completed
          : completed // ignore: cast_nullable_to_non_nullable
              as List<dynamic>,
      cancelled: null == cancelled
          ? _value.cancelled
          : cancelled // ignore: cast_nullable_to_non_nullable
              as List<dynamic>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ANStatisticsModelImplCopyWith<$Res>
    implements $ANStatisticsModelCopyWith<$Res> {
  factory _$$ANStatisticsModelImplCopyWith(_$ANStatisticsModelImpl value,
          $Res Function(_$ANStatisticsModelImpl) then) =
      __$$ANStatisticsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<dynamic> completed, List<dynamic> cancelled});
}

/// @nodoc
class __$$ANStatisticsModelImplCopyWithImpl<$Res>
    extends _$ANStatisticsModelCopyWithImpl<$Res, _$ANStatisticsModelImpl>
    implements _$$ANStatisticsModelImplCopyWith<$Res> {
  __$$ANStatisticsModelImplCopyWithImpl(_$ANStatisticsModelImpl _value,
      $Res Function(_$ANStatisticsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ANStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? completed = null,
    Object? cancelled = null,
  }) {
    return _then(_$ANStatisticsModelImpl(
      completed: null == completed
          ? _value._completed
          : completed // ignore: cast_nullable_to_non_nullable
              as List<dynamic>,
      cancelled: null == cancelled
          ? _value._cancelled
          : cancelled // ignore: cast_nullable_to_non_nullable
              as List<dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ANStatisticsModelImpl implements _ANStatisticsModel {
  const _$ANStatisticsModelImpl(
      {required final List<dynamic> completed,
      required final List<dynamic> cancelled})
      : _completed = completed,
        _cancelled = cancelled;

  factory _$ANStatisticsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ANStatisticsModelImplFromJson(json);

  final List<dynamic> _completed;
  @override
  List<dynamic> get completed {
    if (_completed is EqualUnmodifiableListView) return _completed;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_completed);
  }

  final List<dynamic> _cancelled;
  @override
  List<dynamic> get cancelled {
    if (_cancelled is EqualUnmodifiableListView) return _cancelled;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_cancelled);
  }

  @override
  String toString() {
    return 'ANStatisticsModel(completed: $completed, cancelled: $cancelled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ANStatisticsModelImpl &&
            const DeepCollectionEquality()
                .equals(other._completed, _completed) &&
            const DeepCollectionEquality()
                .equals(other._cancelled, _cancelled));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_completed),
      const DeepCollectionEquality().hash(_cancelled));

  /// Create a copy of ANStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ANStatisticsModelImplCopyWith<_$ANStatisticsModelImpl> get copyWith =>
      __$$ANStatisticsModelImplCopyWithImpl<_$ANStatisticsModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ANStatisticsModelImplToJson(
      this,
    );
  }
}

abstract class _ANStatisticsModel implements ANStatisticsModel {
  const factory _ANStatisticsModel(
      {required final List<dynamic> completed,
      required final List<dynamic> cancelled}) = _$ANStatisticsModelImpl;

  factory _ANStatisticsModel.fromJson(Map<String, dynamic> json) =
      _$ANStatisticsModelImpl.fromJson;

  @override
  List<dynamic> get completed;
  @override
  List<dynamic> get cancelled;

  /// Create a copy of ANStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ANStatisticsModelImplCopyWith<_$ANStatisticsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
