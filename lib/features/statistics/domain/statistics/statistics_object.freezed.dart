// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'statistics_object.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ANStatisticsModel {
  List get completed;
  List get cancelled;

  /// Create a copy of ANStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ANStatisticsModelCopyWith<ANStatisticsModel> get copyWith =>
      _$ANStatisticsModelCopyWithImpl<ANStatisticsModel>(
          this as ANStatisticsModel, _$identity);

  /// Serializes this ANStatisticsModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ANStatisticsModel &&
            const DeepCollectionEquality().equals(other.completed, completed) &&
            const DeepCollectionEquality().equals(other.cancelled, cancelled));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(completed),
      const DeepCollectionEquality().hash(cancelled));

  @override
  String toString() {
    return 'ANStatisticsModel(completed: $completed, cancelled: $cancelled)';
  }
}

/// @nodoc
abstract mixin class $ANStatisticsModelCopyWith<$Res> {
  factory $ANStatisticsModelCopyWith(
          ANStatisticsModel value, $Res Function(ANStatisticsModel) _then) =
      _$ANStatisticsModelCopyWithImpl;
  @useResult
  $Res call({List completed, List cancelled});
}

/// @nodoc
class _$ANStatisticsModelCopyWithImpl<$Res>
    implements $ANStatisticsModelCopyWith<$Res> {
  _$ANStatisticsModelCopyWithImpl(this._self, this._then);

  final ANStatisticsModel _self;
  final $Res Function(ANStatisticsModel) _then;

  /// Create a copy of ANStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? completed = null,
    Object? cancelled = null,
  }) {
    return _then(_self.copyWith(
      completed: null == completed
          ? _self.completed
          : completed // ignore: cast_nullable_to_non_nullable
              as List,
      cancelled: null == cancelled
          ? _self.cancelled
          : cancelled // ignore: cast_nullable_to_non_nullable
              as List,
    ));
  }
}

/// Adds pattern-matching-related methods to [ANStatisticsModel].
extension ANStatisticsModelPatterns on ANStatisticsModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ANStatisticsModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANStatisticsModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ANStatisticsModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANStatisticsModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ANStatisticsModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANStatisticsModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(List completed, List cancelled)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANStatisticsModel() when $default != null:
        return $default(_that.completed, _that.cancelled);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(List completed, List cancelled) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANStatisticsModel():
        return $default(_that.completed, _that.cancelled);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(List completed, List cancelled)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANStatisticsModel() when $default != null:
        return $default(_that.completed, _that.cancelled);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ANStatisticsModel implements ANStatisticsModel {
  const _ANStatisticsModel(
      {required final List completed, required final List cancelled})
      : _completed = completed,
        _cancelled = cancelled;
  factory _ANStatisticsModel.fromJson(Map<String, dynamic> json) =>
      _$ANStatisticsModelFromJson(json);

  final List _completed;
  @override
  List get completed {
    if (_completed is EqualUnmodifiableListView) return _completed;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_completed);
  }

  final List _cancelled;
  @override
  List get cancelled {
    if (_cancelled is EqualUnmodifiableListView) return _cancelled;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_cancelled);
  }

  /// Create a copy of ANStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ANStatisticsModelCopyWith<_ANStatisticsModel> get copyWith =>
      __$ANStatisticsModelCopyWithImpl<_ANStatisticsModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ANStatisticsModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ANStatisticsModel &&
            const DeepCollectionEquality()
                .equals(other._completed, _completed) &&
            const DeepCollectionEquality()
                .equals(other._cancelled, _cancelled));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_completed),
      const DeepCollectionEquality().hash(_cancelled));

  @override
  String toString() {
    return 'ANStatisticsModel(completed: $completed, cancelled: $cancelled)';
  }
}

/// @nodoc
abstract mixin class _$ANStatisticsModelCopyWith<$Res>
    implements $ANStatisticsModelCopyWith<$Res> {
  factory _$ANStatisticsModelCopyWith(
          _ANStatisticsModel value, $Res Function(_ANStatisticsModel) _then) =
      __$ANStatisticsModelCopyWithImpl;
  @override
  @useResult
  $Res call({List completed, List cancelled});
}

/// @nodoc
class __$ANStatisticsModelCopyWithImpl<$Res>
    implements _$ANStatisticsModelCopyWith<$Res> {
  __$ANStatisticsModelCopyWithImpl(this._self, this._then);

  final _ANStatisticsModel _self;
  final $Res Function(_ANStatisticsModel) _then;

  /// Create a copy of ANStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? completed = null,
    Object? cancelled = null,
  }) {
    return _then(_ANStatisticsModel(
      completed: null == completed
          ? _self._completed
          : completed // ignore: cast_nullable_to_non_nullable
              as List,
      cancelled: null == cancelled
          ? _self._cancelled
          : cancelled // ignore: cast_nullable_to_non_nullable
              as List,
    ));
  }
}

// dart format on
