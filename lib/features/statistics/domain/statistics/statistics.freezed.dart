// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'statistics.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ANServiceProviderStatisticsModel {
  ANStatisticsModel get lastMonth;
  ANStatisticsModel get thisMonth;
  ANStatisticsModel get all;

  /// Create a copy of ANServiceProviderStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ANServiceProviderStatisticsModelCopyWith<ANServiceProviderStatisticsModel>
      get copyWith => _$ANServiceProviderStatisticsModelCopyWithImpl<
              ANServiceProviderStatisticsModel>(
          this as ANServiceProviderStatisticsModel, _$identity);

  /// Serializes this ANServiceProviderStatisticsModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ANServiceProviderStatisticsModel &&
            (identical(other.lastMonth, lastMonth) ||
                other.lastMonth == lastMonth) &&
            (identical(other.thisMonth, thisMonth) ||
                other.thisMonth == thisMonth) &&
            (identical(other.all, all) || other.all == all));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, lastMonth, thisMonth, all);

  @override
  String toString() {
    return 'ANServiceProviderStatisticsModel(lastMonth: $lastMonth, thisMonth: $thisMonth, all: $all)';
  }
}

/// @nodoc
abstract mixin class $ANServiceProviderStatisticsModelCopyWith<$Res> {
  factory $ANServiceProviderStatisticsModelCopyWith(
          ANServiceProviderStatisticsModel value,
          $Res Function(ANServiceProviderStatisticsModel) _then) =
      _$ANServiceProviderStatisticsModelCopyWithImpl;
  @useResult
  $Res call(
      {ANStatisticsModel lastMonth,
      ANStatisticsModel thisMonth,
      ANStatisticsModel all});

  $ANStatisticsModelCopyWith<$Res> get lastMonth;
  $ANStatisticsModelCopyWith<$Res> get thisMonth;
  $ANStatisticsModelCopyWith<$Res> get all;
}

/// @nodoc
class _$ANServiceProviderStatisticsModelCopyWithImpl<$Res>
    implements $ANServiceProviderStatisticsModelCopyWith<$Res> {
  _$ANServiceProviderStatisticsModelCopyWithImpl(this._self, this._then);

  final ANServiceProviderStatisticsModel _self;
  final $Res Function(ANServiceProviderStatisticsModel) _then;

  /// Create a copy of ANServiceProviderStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lastMonth = null,
    Object? thisMonth = null,
    Object? all = null,
  }) {
    return _then(_self.copyWith(
      lastMonth: null == lastMonth
          ? _self.lastMonth
          : lastMonth // ignore: cast_nullable_to_non_nullable
              as ANStatisticsModel,
      thisMonth: null == thisMonth
          ? _self.thisMonth
          : thisMonth // ignore: cast_nullable_to_non_nullable
              as ANStatisticsModel,
      all: null == all
          ? _self.all
          : all // ignore: cast_nullable_to_non_nullable
              as ANStatisticsModel,
    ));
  }

  /// Create a copy of ANServiceProviderStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ANStatisticsModelCopyWith<$Res> get lastMonth {
    return $ANStatisticsModelCopyWith<$Res>(_self.lastMonth, (value) {
      return _then(_self.copyWith(lastMonth: value));
    });
  }

  /// Create a copy of ANServiceProviderStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ANStatisticsModelCopyWith<$Res> get thisMonth {
    return $ANStatisticsModelCopyWith<$Res>(_self.thisMonth, (value) {
      return _then(_self.copyWith(thisMonth: value));
    });
  }

  /// Create a copy of ANServiceProviderStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ANStatisticsModelCopyWith<$Res> get all {
    return $ANStatisticsModelCopyWith<$Res>(_self.all, (value) {
      return _then(_self.copyWith(all: value));
    });
  }
}

/// Adds pattern-matching-related methods to [ANServiceProviderStatisticsModel].
extension ANServiceProviderStatisticsModelPatterns
    on ANServiceProviderStatisticsModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ANServiceProviderStatisticsModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANServiceProviderStatisticsModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ANServiceProviderStatisticsModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANServiceProviderStatisticsModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ANServiceProviderStatisticsModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANServiceProviderStatisticsModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(ANStatisticsModel lastMonth, ANStatisticsModel thisMonth,
            ANStatisticsModel all)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANServiceProviderStatisticsModel() when $default != null:
        return $default(_that.lastMonth, _that.thisMonth, _that.all);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(ANStatisticsModel lastMonth, ANStatisticsModel thisMonth,
            ANStatisticsModel all)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANServiceProviderStatisticsModel():
        return $default(_that.lastMonth, _that.thisMonth, _that.all);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(ANStatisticsModel lastMonth, ANStatisticsModel thisMonth,
            ANStatisticsModel all)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANServiceProviderStatisticsModel() when $default != null:
        return $default(_that.lastMonth, _that.thisMonth, _that.all);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ANServiceProviderStatisticsModel
    implements ANServiceProviderStatisticsModel {
  const _ANServiceProviderStatisticsModel(
      {required this.lastMonth, required this.thisMonth, required this.all});
  factory _ANServiceProviderStatisticsModel.fromJson(
          Map<String, dynamic> json) =>
      _$ANServiceProviderStatisticsModelFromJson(json);

  @override
  final ANStatisticsModel lastMonth;
  @override
  final ANStatisticsModel thisMonth;
  @override
  final ANStatisticsModel all;

  /// Create a copy of ANServiceProviderStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ANServiceProviderStatisticsModelCopyWith<_ANServiceProviderStatisticsModel>
      get copyWith => __$ANServiceProviderStatisticsModelCopyWithImpl<
          _ANServiceProviderStatisticsModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ANServiceProviderStatisticsModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ANServiceProviderStatisticsModel &&
            (identical(other.lastMonth, lastMonth) ||
                other.lastMonth == lastMonth) &&
            (identical(other.thisMonth, thisMonth) ||
                other.thisMonth == thisMonth) &&
            (identical(other.all, all) || other.all == all));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, lastMonth, thisMonth, all);

  @override
  String toString() {
    return 'ANServiceProviderStatisticsModel(lastMonth: $lastMonth, thisMonth: $thisMonth, all: $all)';
  }
}

/// @nodoc
abstract mixin class _$ANServiceProviderStatisticsModelCopyWith<$Res>
    implements $ANServiceProviderStatisticsModelCopyWith<$Res> {
  factory _$ANServiceProviderStatisticsModelCopyWith(
          _ANServiceProviderStatisticsModel value,
          $Res Function(_ANServiceProviderStatisticsModel) _then) =
      __$ANServiceProviderStatisticsModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {ANStatisticsModel lastMonth,
      ANStatisticsModel thisMonth,
      ANStatisticsModel all});

  @override
  $ANStatisticsModelCopyWith<$Res> get lastMonth;
  @override
  $ANStatisticsModelCopyWith<$Res> get thisMonth;
  @override
  $ANStatisticsModelCopyWith<$Res> get all;
}

/// @nodoc
class __$ANServiceProviderStatisticsModelCopyWithImpl<$Res>
    implements _$ANServiceProviderStatisticsModelCopyWith<$Res> {
  __$ANServiceProviderStatisticsModelCopyWithImpl(this._self, this._then);

  final _ANServiceProviderStatisticsModel _self;
  final $Res Function(_ANServiceProviderStatisticsModel) _then;

  /// Create a copy of ANServiceProviderStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? lastMonth = null,
    Object? thisMonth = null,
    Object? all = null,
  }) {
    return _then(_ANServiceProviderStatisticsModel(
      lastMonth: null == lastMonth
          ? _self.lastMonth
          : lastMonth // ignore: cast_nullable_to_non_nullable
              as ANStatisticsModel,
      thisMonth: null == thisMonth
          ? _self.thisMonth
          : thisMonth // ignore: cast_nullable_to_non_nullable
              as ANStatisticsModel,
      all: null == all
          ? _self.all
          : all // ignore: cast_nullable_to_non_nullable
              as ANStatisticsModel,
    ));
  }

  /// Create a copy of ANServiceProviderStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ANStatisticsModelCopyWith<$Res> get lastMonth {
    return $ANStatisticsModelCopyWith<$Res>(_self.lastMonth, (value) {
      return _then(_self.copyWith(lastMonth: value));
    });
  }

  /// Create a copy of ANServiceProviderStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ANStatisticsModelCopyWith<$Res> get thisMonth {
    return $ANStatisticsModelCopyWith<$Res>(_self.thisMonth, (value) {
      return _then(_self.copyWith(thisMonth: value));
    });
  }

  /// Create a copy of ANServiceProviderStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ANStatisticsModelCopyWith<$Res> get all {
    return $ANStatisticsModelCopyWith<$Res>(_self.all, (value) {
      return _then(_self.copyWith(all: value));
    });
  }
}

// dart format on
