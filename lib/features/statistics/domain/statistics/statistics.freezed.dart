// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'statistics.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ANServiceProviderStatisticsModel _$ANServiceProviderStatisticsModelFromJson(
    Map<String, dynamic> json) {
  return _ANServiceProviderStatisticsModel.fromJson(json);
}

/// @nodoc
mixin _$ANServiceProviderStatisticsModel {
  ANStatisticsModel get lastMonth => throw _privateConstructorUsedError;
  ANStatisticsModel get thisMonth => throw _privateConstructorUsedError;
  ANStatisticsModel get all => throw _privateConstructorUsedError;

  /// Serializes this ANServiceProviderStatisticsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ANServiceProviderStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ANServiceProviderStatisticsModelCopyWith<ANServiceProviderStatisticsModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANServiceProviderStatisticsModelCopyWith<$Res> {
  factory $ANServiceProviderStatisticsModelCopyWith(
          ANServiceProviderStatisticsModel value,
          $Res Function(ANServiceProviderStatisticsModel) then) =
      _$ANServiceProviderStatisticsModelCopyWithImpl<$Res,
          ANServiceProviderStatisticsModel>;
  @useResult
  $Res call(
      {ANStatisticsModel lastMonth,
      ANStatisticsModel thisMonth,
      ANStatisticsModel all});

  $ANStatisticsModelCopyWith<$Res> get lastMonth;
  $ANStatisticsModelCopyWith<$Res> get thisMonth;
  $ANStatisticsModelCopyWith<$Res> get all;
}

/// @nodoc
class _$ANServiceProviderStatisticsModelCopyWithImpl<$Res,
        $Val extends ANServiceProviderStatisticsModel>
    implements $ANServiceProviderStatisticsModelCopyWith<$Res> {
  _$ANServiceProviderStatisticsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ANServiceProviderStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lastMonth = null,
    Object? thisMonth = null,
    Object? all = null,
  }) {
    return _then(_value.copyWith(
      lastMonth: null == lastMonth
          ? _value.lastMonth
          : lastMonth // ignore: cast_nullable_to_non_nullable
              as ANStatisticsModel,
      thisMonth: null == thisMonth
          ? _value.thisMonth
          : thisMonth // ignore: cast_nullable_to_non_nullable
              as ANStatisticsModel,
      all: null == all
          ? _value.all
          : all // ignore: cast_nullable_to_non_nullable
              as ANStatisticsModel,
    ) as $Val);
  }

  /// Create a copy of ANServiceProviderStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ANStatisticsModelCopyWith<$Res> get lastMonth {
    return $ANStatisticsModelCopyWith<$Res>(_value.lastMonth, (value) {
      return _then(_value.copyWith(lastMonth: value) as $Val);
    });
  }

  /// Create a copy of ANServiceProviderStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ANStatisticsModelCopyWith<$Res> get thisMonth {
    return $ANStatisticsModelCopyWith<$Res>(_value.thisMonth, (value) {
      return _then(_value.copyWith(thisMonth: value) as $Val);
    });
  }

  /// Create a copy of ANServiceProviderStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ANStatisticsModelCopyWith<$Res> get all {
    return $ANStatisticsModelCopyWith<$Res>(_value.all, (value) {
      return _then(_value.copyWith(all: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ANServiceProviderStatisticsModelImplCopyWith<$Res>
    implements $ANServiceProviderStatisticsModelCopyWith<$Res> {
  factory _$$ANServiceProviderStatisticsModelImplCopyWith(
          _$ANServiceProviderStatisticsModelImpl value,
          $Res Function(_$ANServiceProviderStatisticsModelImpl) then) =
      __$$ANServiceProviderStatisticsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {ANStatisticsModel lastMonth,
      ANStatisticsModel thisMonth,
      ANStatisticsModel all});

  @override
  $ANStatisticsModelCopyWith<$Res> get lastMonth;
  @override
  $ANStatisticsModelCopyWith<$Res> get thisMonth;
  @override
  $ANStatisticsModelCopyWith<$Res> get all;
}

/// @nodoc
class __$$ANServiceProviderStatisticsModelImplCopyWithImpl<$Res>
    extends _$ANServiceProviderStatisticsModelCopyWithImpl<$Res,
        _$ANServiceProviderStatisticsModelImpl>
    implements _$$ANServiceProviderStatisticsModelImplCopyWith<$Res> {
  __$$ANServiceProviderStatisticsModelImplCopyWithImpl(
      _$ANServiceProviderStatisticsModelImpl _value,
      $Res Function(_$ANServiceProviderStatisticsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ANServiceProviderStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lastMonth = null,
    Object? thisMonth = null,
    Object? all = null,
  }) {
    return _then(_$ANServiceProviderStatisticsModelImpl(
      lastMonth: null == lastMonth
          ? _value.lastMonth
          : lastMonth // ignore: cast_nullable_to_non_nullable
              as ANStatisticsModel,
      thisMonth: null == thisMonth
          ? _value.thisMonth
          : thisMonth // ignore: cast_nullable_to_non_nullable
              as ANStatisticsModel,
      all: null == all
          ? _value.all
          : all // ignore: cast_nullable_to_non_nullable
              as ANStatisticsModel,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ANServiceProviderStatisticsModelImpl
    implements _ANServiceProviderStatisticsModel {
  const _$ANServiceProviderStatisticsModelImpl(
      {required this.lastMonth, required this.thisMonth, required this.all});

  factory _$ANServiceProviderStatisticsModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ANServiceProviderStatisticsModelImplFromJson(json);

  @override
  final ANStatisticsModel lastMonth;
  @override
  final ANStatisticsModel thisMonth;
  @override
  final ANStatisticsModel all;

  @override
  String toString() {
    return 'ANServiceProviderStatisticsModel(lastMonth: $lastMonth, thisMonth: $thisMonth, all: $all)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ANServiceProviderStatisticsModelImpl &&
            (identical(other.lastMonth, lastMonth) ||
                other.lastMonth == lastMonth) &&
            (identical(other.thisMonth, thisMonth) ||
                other.thisMonth == thisMonth) &&
            (identical(other.all, all) || other.all == all));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, lastMonth, thisMonth, all);

  /// Create a copy of ANServiceProviderStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ANServiceProviderStatisticsModelImplCopyWith<
          _$ANServiceProviderStatisticsModelImpl>
      get copyWith => __$$ANServiceProviderStatisticsModelImplCopyWithImpl<
          _$ANServiceProviderStatisticsModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ANServiceProviderStatisticsModelImplToJson(
      this,
    );
  }
}

abstract class _ANServiceProviderStatisticsModel
    implements ANServiceProviderStatisticsModel {
  const factory _ANServiceProviderStatisticsModel(
          {required final ANStatisticsModel lastMonth,
          required final ANStatisticsModel thisMonth,
          required final ANStatisticsModel all}) =
      _$ANServiceProviderStatisticsModelImpl;

  factory _ANServiceProviderStatisticsModel.fromJson(
          Map<String, dynamic> json) =
      _$ANServiceProviderStatisticsModelImpl.fromJson;

  @override
  ANStatisticsModel get lastMonth;
  @override
  ANStatisticsModel get thisMonth;
  @override
  ANStatisticsModel get all;

  /// Create a copy of ANServiceProviderStatisticsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ANServiceProviderStatisticsModelImplCopyWith<
          _$ANServiceProviderStatisticsModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
