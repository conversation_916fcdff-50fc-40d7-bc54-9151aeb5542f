// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'service_types.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ANServiceTypesModel _$ANServiceTypesModelFromJson(Map<String, dynamic> json) {
  return _ANServiceTypesModel.fromJson(json);
}

/// @nodoc
mixin _$ANServiceTypesModel {
  String get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'main_category')
  String get mainCategory => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get color => throw _privateConstructorUsedError;

  /// Serializes this ANServiceTypesModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ANServiceTypesModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ANServiceTypesModelCopyWith<ANServiceTypesModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANServiceTypesModelCopyWith<$Res> {
  factory $ANServiceTypesModelCopyWith(
          ANServiceTypesModel value, $Res Function(ANServiceTypesModel) then) =
      _$ANServiceTypesModelCopyWithImpl<$Res, ANServiceTypesModel>;
  @useResult
  $Res call(
      {String id,
      @JsonKey(name: 'main_category') String mainCategory,
      String name,
      String? description,
      String? color});
}

/// @nodoc
class _$ANServiceTypesModelCopyWithImpl<$Res, $Val extends ANServiceTypesModel>
    implements $ANServiceTypesModelCopyWith<$Res> {
  _$ANServiceTypesModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ANServiceTypesModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? mainCategory = null,
    Object? name = null,
    Object? description = freezed,
    Object? color = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      mainCategory: null == mainCategory
          ? _value.mainCategory
          : mainCategory // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ANServiceTypesModelImplCopyWith<$Res>
    implements $ANServiceTypesModelCopyWith<$Res> {
  factory _$$ANServiceTypesModelImplCopyWith(_$ANServiceTypesModelImpl value,
          $Res Function(_$ANServiceTypesModelImpl) then) =
      __$$ANServiceTypesModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      @JsonKey(name: 'main_category') String mainCategory,
      String name,
      String? description,
      String? color});
}

/// @nodoc
class __$$ANServiceTypesModelImplCopyWithImpl<$Res>
    extends _$ANServiceTypesModelCopyWithImpl<$Res, _$ANServiceTypesModelImpl>
    implements _$$ANServiceTypesModelImplCopyWith<$Res> {
  __$$ANServiceTypesModelImplCopyWithImpl(_$ANServiceTypesModelImpl _value,
      $Res Function(_$ANServiceTypesModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ANServiceTypesModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? mainCategory = null,
    Object? name = null,
    Object? description = freezed,
    Object? color = freezed,
  }) {
    return _then(_$ANServiceTypesModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      mainCategory: null == mainCategory
          ? _value.mainCategory
          : mainCategory // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ANServiceTypesModelImpl implements _ANServiceTypesModel {
  const _$ANServiceTypesModelImpl(
      {required this.id,
      @JsonKey(name: 'main_category') required this.mainCategory,
      required this.name,
      this.description,
      this.color});

  factory _$ANServiceTypesModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ANServiceTypesModelImplFromJson(json);

  @override
  final String id;
  @override
  @JsonKey(name: 'main_category')
  final String mainCategory;
  @override
  final String name;
  @override
  final String? description;
  @override
  final String? color;

  @override
  String toString() {
    return 'ANServiceTypesModel(id: $id, mainCategory: $mainCategory, name: $name, description: $description, color: $color)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ANServiceTypesModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.mainCategory, mainCategory) ||
                other.mainCategory == mainCategory) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.color, color) || other.color == color));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, mainCategory, name, description, color);

  /// Create a copy of ANServiceTypesModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ANServiceTypesModelImplCopyWith<_$ANServiceTypesModelImpl> get copyWith =>
      __$$ANServiceTypesModelImplCopyWithImpl<_$ANServiceTypesModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ANServiceTypesModelImplToJson(
      this,
    );
  }
}

abstract class _ANServiceTypesModel implements ANServiceTypesModel {
  const factory _ANServiceTypesModel(
      {required final String id,
      @JsonKey(name: 'main_category') required final String mainCategory,
      required final String name,
      final String? description,
      final String? color}) = _$ANServiceTypesModelImpl;

  factory _ANServiceTypesModel.fromJson(Map<String, dynamic> json) =
      _$ANServiceTypesModelImpl.fromJson;

  @override
  String get id;
  @override
  @JsonKey(name: 'main_category')
  String get mainCategory;
  @override
  String get name;
  @override
  String? get description;
  @override
  String? get color;

  /// Create a copy of ANServiceTypesModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ANServiceTypesModelImplCopyWith<_$ANServiceTypesModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
