// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'service_types.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ANServiceTypesModel {
  String get id;
  @JsonKey(name: 'main_category')
  String get mainCategory;
  String get name;
  String? get description;
  String? get color;

  /// Create a copy of ANServiceTypesModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ANServiceTypesModelCopyWith<ANServiceTypesModel> get copyWith =>
      _$ANServiceTypesModelCopyWithImpl<ANServiceTypesModel>(
          this as ANServiceTypesModel, _$identity);

  /// Serializes this ANServiceTypesModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ANServiceTypesModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.mainCategory, mainCategory) ||
                other.mainCategory == mainCategory) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.color, color) || other.color == color));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, mainCategory, name, description, color);

  @override
  String toString() {
    return 'ANServiceTypesModel(id: $id, mainCategory: $mainCategory, name: $name, description: $description, color: $color)';
  }
}

/// @nodoc
abstract mixin class $ANServiceTypesModelCopyWith<$Res> {
  factory $ANServiceTypesModelCopyWith(
          ANServiceTypesModel value, $Res Function(ANServiceTypesModel) _then) =
      _$ANServiceTypesModelCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      @JsonKey(name: 'main_category') String mainCategory,
      String name,
      String? description,
      String? color});
}

/// @nodoc
class _$ANServiceTypesModelCopyWithImpl<$Res>
    implements $ANServiceTypesModelCopyWith<$Res> {
  _$ANServiceTypesModelCopyWithImpl(this._self, this._then);

  final ANServiceTypesModel _self;
  final $Res Function(ANServiceTypesModel) _then;

  /// Create a copy of ANServiceTypesModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? mainCategory = null,
    Object? name = null,
    Object? description = freezed,
    Object? color = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      mainCategory: null == mainCategory
          ? _self.mainCategory
          : mainCategory // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      color: freezed == color
          ? _self.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [ANServiceTypesModel].
extension ANServiceTypesModelPatterns on ANServiceTypesModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ANServiceTypesModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANServiceTypesModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ANServiceTypesModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANServiceTypesModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ANServiceTypesModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANServiceTypesModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String id,
            @JsonKey(name: 'main_category') String mainCategory,
            String name,
            String? description,
            String? color)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANServiceTypesModel() when $default != null:
        return $default(_that.id, _that.mainCategory, _that.name,
            _that.description, _that.color);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String id,
            @JsonKey(name: 'main_category') String mainCategory,
            String name,
            String? description,
            String? color)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANServiceTypesModel():
        return $default(_that.id, _that.mainCategory, _that.name,
            _that.description, _that.color);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String id,
            @JsonKey(name: 'main_category') String mainCategory,
            String name,
            String? description,
            String? color)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANServiceTypesModel() when $default != null:
        return $default(_that.id, _that.mainCategory, _that.name,
            _that.description, _that.color);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ANServiceTypesModel implements ANServiceTypesModel {
  const _ANServiceTypesModel(
      {required this.id,
      @JsonKey(name: 'main_category') required this.mainCategory,
      required this.name,
      this.description,
      this.color});
  factory _ANServiceTypesModel.fromJson(Map<String, dynamic> json) =>
      _$ANServiceTypesModelFromJson(json);

  @override
  final String id;
  @override
  @JsonKey(name: 'main_category')
  final String mainCategory;
  @override
  final String name;
  @override
  final String? description;
  @override
  final String? color;

  /// Create a copy of ANServiceTypesModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ANServiceTypesModelCopyWith<_ANServiceTypesModel> get copyWith =>
      __$ANServiceTypesModelCopyWithImpl<_ANServiceTypesModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ANServiceTypesModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ANServiceTypesModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.mainCategory, mainCategory) ||
                other.mainCategory == mainCategory) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.color, color) || other.color == color));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, mainCategory, name, description, color);

  @override
  String toString() {
    return 'ANServiceTypesModel(id: $id, mainCategory: $mainCategory, name: $name, description: $description, color: $color)';
  }
}

/// @nodoc
abstract mixin class _$ANServiceTypesModelCopyWith<$Res>
    implements $ANServiceTypesModelCopyWith<$Res> {
  factory _$ANServiceTypesModelCopyWith(_ANServiceTypesModel value,
          $Res Function(_ANServiceTypesModel) _then) =
      __$ANServiceTypesModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      @JsonKey(name: 'main_category') String mainCategory,
      String name,
      String? description,
      String? color});
}

/// @nodoc
class __$ANServiceTypesModelCopyWithImpl<$Res>
    implements _$ANServiceTypesModelCopyWith<$Res> {
  __$ANServiceTypesModelCopyWithImpl(this._self, this._then);

  final _ANServiceTypesModel _self;
  final $Res Function(_ANServiceTypesModel) _then;

  /// Create a copy of ANServiceTypesModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? mainCategory = null,
    Object? name = null,
    Object? description = freezed,
    Object? color = freezed,
  }) {
    return _then(_ANServiceTypesModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      mainCategory: null == mainCategory
          ? _self.mainCategory
          : mainCategory // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      color: freezed == color
          ? _self.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
