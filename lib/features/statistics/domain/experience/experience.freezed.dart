// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'experience.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ANExperienceModel _$ANExperienceModelFromJson(Map<String, dynamic> json) {
  return _ANExperienceModel.fromJson(json);
}

/// @nodoc
mixin _$ANExperienceModel {
  String get id => throw _privateConstructorUsedError;
  String? get jobPosition => throw _privateConstructorUsedError;
  String? get institution => throw _privateConstructorUsedError;
  DateTime? get startDate => throw _privateConstructorUsedError;
  bool? get currentlyWorking => throw _privateConstructorUsedError;
  DateTime? get endDate => throw _privateConstructorUsedError;

  /// Serializes this ANExperienceModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ANExperienceModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ANExperienceModelCopyWith<ANExperienceModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANExperienceModelCopyWith<$Res> {
  factory $ANExperienceModelCopyWith(
          ANExperienceModel value, $Res Function(ANExperienceModel) then) =
      _$ANExperienceModelCopyWithImpl<$Res, ANExperienceModel>;
  @useResult
  $Res call(
      {String id,
      String? jobPosition,
      String? institution,
      DateTime? startDate,
      bool? currentlyWorking,
      DateTime? endDate});
}

/// @nodoc
class _$ANExperienceModelCopyWithImpl<$Res, $Val extends ANExperienceModel>
    implements $ANExperienceModelCopyWith<$Res> {
  _$ANExperienceModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ANExperienceModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? jobPosition = freezed,
    Object? institution = freezed,
    Object? startDate = freezed,
    Object? currentlyWorking = freezed,
    Object? endDate = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      jobPosition: freezed == jobPosition
          ? _value.jobPosition
          : jobPosition // ignore: cast_nullable_to_non_nullable
              as String?,
      institution: freezed == institution
          ? _value.institution
          : institution // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      currentlyWorking: freezed == currentlyWorking
          ? _value.currentlyWorking
          : currentlyWorking // ignore: cast_nullable_to_non_nullable
              as bool?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ANExperienceModelImplCopyWith<$Res>
    implements $ANExperienceModelCopyWith<$Res> {
  factory _$$ANExperienceModelImplCopyWith(_$ANExperienceModelImpl value,
          $Res Function(_$ANExperienceModelImpl) then) =
      __$$ANExperienceModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String? jobPosition,
      String? institution,
      DateTime? startDate,
      bool? currentlyWorking,
      DateTime? endDate});
}

/// @nodoc
class __$$ANExperienceModelImplCopyWithImpl<$Res>
    extends _$ANExperienceModelCopyWithImpl<$Res, _$ANExperienceModelImpl>
    implements _$$ANExperienceModelImplCopyWith<$Res> {
  __$$ANExperienceModelImplCopyWithImpl(_$ANExperienceModelImpl _value,
      $Res Function(_$ANExperienceModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ANExperienceModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? jobPosition = freezed,
    Object? institution = freezed,
    Object? startDate = freezed,
    Object? currentlyWorking = freezed,
    Object? endDate = freezed,
  }) {
    return _then(_$ANExperienceModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      jobPosition: freezed == jobPosition
          ? _value.jobPosition
          : jobPosition // ignore: cast_nullable_to_non_nullable
              as String?,
      institution: freezed == institution
          ? _value.institution
          : institution // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      currentlyWorking: freezed == currentlyWorking
          ? _value.currentlyWorking
          : currentlyWorking // ignore: cast_nullable_to_non_nullable
              as bool?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ANExperienceModelImpl implements _ANExperienceModel {
  const _$ANExperienceModelImpl(
      {required this.id,
      this.jobPosition,
      this.institution,
      this.startDate,
      this.currentlyWorking,
      this.endDate});

  factory _$ANExperienceModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ANExperienceModelImplFromJson(json);

  @override
  final String id;
  @override
  final String? jobPosition;
  @override
  final String? institution;
  @override
  final DateTime? startDate;
  @override
  final bool? currentlyWorking;
  @override
  final DateTime? endDate;

  @override
  String toString() {
    return 'ANExperienceModel(id: $id, jobPosition: $jobPosition, institution: $institution, startDate: $startDate, currentlyWorking: $currentlyWorking, endDate: $endDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ANExperienceModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.jobPosition, jobPosition) ||
                other.jobPosition == jobPosition) &&
            (identical(other.institution, institution) ||
                other.institution == institution) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.currentlyWorking, currentlyWorking) ||
                other.currentlyWorking == currentlyWorking) &&
            (identical(other.endDate, endDate) || other.endDate == endDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, jobPosition, institution,
      startDate, currentlyWorking, endDate);

  /// Create a copy of ANExperienceModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ANExperienceModelImplCopyWith<_$ANExperienceModelImpl> get copyWith =>
      __$$ANExperienceModelImplCopyWithImpl<_$ANExperienceModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ANExperienceModelImplToJson(
      this,
    );
  }
}

abstract class _ANExperienceModel implements ANExperienceModel {
  const factory _ANExperienceModel(
      {required final String id,
      final String? jobPosition,
      final String? institution,
      final DateTime? startDate,
      final bool? currentlyWorking,
      final DateTime? endDate}) = _$ANExperienceModelImpl;

  factory _ANExperienceModel.fromJson(Map<String, dynamic> json) =
      _$ANExperienceModelImpl.fromJson;

  @override
  String get id;
  @override
  String? get jobPosition;
  @override
  String? get institution;
  @override
  DateTime? get startDate;
  @override
  bool? get currentlyWorking;
  @override
  DateTime? get endDate;

  /// Create a copy of ANExperienceModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ANExperienceModelImplCopyWith<_$ANExperienceModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
