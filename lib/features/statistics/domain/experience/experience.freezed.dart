// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'experience.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ANExperienceModel {
  String get id;
  String? get jobPosition;
  String? get institution;
  DateTime? get startDate;
  bool? get currentlyWorking;
  DateTime? get endDate;

  /// Create a copy of ANExperienceModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ANExperienceModelCopyWith<ANExperienceModel> get copyWith =>
      _$ANExperienceModelCopyWithImpl<ANExperienceModel>(
          this as ANExperienceModel, _$identity);

  /// Serializes this ANExperienceModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ANExperienceModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.jobPosition, jobPosition) ||
                other.jobPosition == jobPosition) &&
            (identical(other.institution, institution) ||
                other.institution == institution) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.currentlyWorking, currentlyWorking) ||
                other.currentlyWorking == currentlyWorking) &&
            (identical(other.endDate, endDate) || other.endDate == endDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, jobPosition, institution,
      startDate, currentlyWorking, endDate);

  @override
  String toString() {
    return 'ANExperienceModel(id: $id, jobPosition: $jobPosition, institution: $institution, startDate: $startDate, currentlyWorking: $currentlyWorking, endDate: $endDate)';
  }
}

/// @nodoc
abstract mixin class $ANExperienceModelCopyWith<$Res> {
  factory $ANExperienceModelCopyWith(
          ANExperienceModel value, $Res Function(ANExperienceModel) _then) =
      _$ANExperienceModelCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String? jobPosition,
      String? institution,
      DateTime? startDate,
      bool? currentlyWorking,
      DateTime? endDate});
}

/// @nodoc
class _$ANExperienceModelCopyWithImpl<$Res>
    implements $ANExperienceModelCopyWith<$Res> {
  _$ANExperienceModelCopyWithImpl(this._self, this._then);

  final ANExperienceModel _self;
  final $Res Function(ANExperienceModel) _then;

  /// Create a copy of ANExperienceModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? jobPosition = freezed,
    Object? institution = freezed,
    Object? startDate = freezed,
    Object? currentlyWorking = freezed,
    Object? endDate = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      jobPosition: freezed == jobPosition
          ? _self.jobPosition
          : jobPosition // ignore: cast_nullable_to_non_nullable
              as String?,
      institution: freezed == institution
          ? _self.institution
          : institution // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _self.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      currentlyWorking: freezed == currentlyWorking
          ? _self.currentlyWorking
          : currentlyWorking // ignore: cast_nullable_to_non_nullable
              as bool?,
      endDate: freezed == endDate
          ? _self.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// Adds pattern-matching-related methods to [ANExperienceModel].
extension ANExperienceModelPatterns on ANExperienceModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ANExperienceModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANExperienceModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ANExperienceModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANExperienceModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ANExperienceModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANExperienceModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String id, String? jobPosition, String? institution,
            DateTime? startDate, bool? currentlyWorking, DateTime? endDate)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ANExperienceModel() when $default != null:
        return $default(_that.id, _that.jobPosition, _that.institution,
            _that.startDate, _that.currentlyWorking, _that.endDate);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String id, String? jobPosition, String? institution,
            DateTime? startDate, bool? currentlyWorking, DateTime? endDate)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANExperienceModel():
        return $default(_that.id, _that.jobPosition, _that.institution,
            _that.startDate, _that.currentlyWorking, _that.endDate);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String id, String? jobPosition, String? institution,
            DateTime? startDate, bool? currentlyWorking, DateTime? endDate)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ANExperienceModel() when $default != null:
        return $default(_that.id, _that.jobPosition, _that.institution,
            _that.startDate, _that.currentlyWorking, _that.endDate);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ANExperienceModel implements ANExperienceModel {
  const _ANExperienceModel(
      {required this.id,
      this.jobPosition,
      this.institution,
      this.startDate,
      this.currentlyWorking,
      this.endDate});
  factory _ANExperienceModel.fromJson(Map<String, dynamic> json) =>
      _$ANExperienceModelFromJson(json);

  @override
  final String id;
  @override
  final String? jobPosition;
  @override
  final String? institution;
  @override
  final DateTime? startDate;
  @override
  final bool? currentlyWorking;
  @override
  final DateTime? endDate;

  /// Create a copy of ANExperienceModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ANExperienceModelCopyWith<_ANExperienceModel> get copyWith =>
      __$ANExperienceModelCopyWithImpl<_ANExperienceModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ANExperienceModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ANExperienceModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.jobPosition, jobPosition) ||
                other.jobPosition == jobPosition) &&
            (identical(other.institution, institution) ||
                other.institution == institution) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.currentlyWorking, currentlyWorking) ||
                other.currentlyWorking == currentlyWorking) &&
            (identical(other.endDate, endDate) || other.endDate == endDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, jobPosition, institution,
      startDate, currentlyWorking, endDate);

  @override
  String toString() {
    return 'ANExperienceModel(id: $id, jobPosition: $jobPosition, institution: $institution, startDate: $startDate, currentlyWorking: $currentlyWorking, endDate: $endDate)';
  }
}

/// @nodoc
abstract mixin class _$ANExperienceModelCopyWith<$Res>
    implements $ANExperienceModelCopyWith<$Res> {
  factory _$ANExperienceModelCopyWith(
          _ANExperienceModel value, $Res Function(_ANExperienceModel) _then) =
      __$ANExperienceModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String? jobPosition,
      String? institution,
      DateTime? startDate,
      bool? currentlyWorking,
      DateTime? endDate});
}

/// @nodoc
class __$ANExperienceModelCopyWithImpl<$Res>
    implements _$ANExperienceModelCopyWith<$Res> {
  __$ANExperienceModelCopyWithImpl(this._self, this._then);

  final _ANExperienceModel _self;
  final $Res Function(_ANExperienceModel) _then;

  /// Create a copy of ANExperienceModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? jobPosition = freezed,
    Object? institution = freezed,
    Object? startDate = freezed,
    Object? currentlyWorking = freezed,
    Object? endDate = freezed,
  }) {
    return _then(_ANExperienceModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      jobPosition: freezed == jobPosition
          ? _self.jobPosition
          : jobPosition // ignore: cast_nullable_to_non_nullable
              as String?,
      institution: freezed == institution
          ? _self.institution
          : institution // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _self.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      currentlyWorking: freezed == currentlyWorking
          ? _self.currentlyWorking
          : currentlyWorking // ignore: cast_nullable_to_non_nullable
              as bool?,
      endDate: freezed == endDate
          ? _self.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

// dart format on
