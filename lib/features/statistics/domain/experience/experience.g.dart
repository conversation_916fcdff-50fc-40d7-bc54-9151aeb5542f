// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'experience.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ANExperienceModelImpl _$$ANExperienceModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ANExperienceModelImpl(
      id: json['id'] as String,
      jobPosition: json['jobPosition'] as String?,
      institution: json['institution'] as String?,
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      currentlyWorking: json['currentlyWorking'] as bool?,
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
    );

Map<String, dynamic> _$$ANExperienceModelImplToJson(
        _$ANExperienceModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'jobPosition': instance.jobPosition,
      'institution': instance.institution,
      'startDate': instance.startDate?.toIso8601String(),
      'currentlyWorking': instance.currentlyWorking,
      'endDate': instance.endDate?.toIso8601String(),
    };
